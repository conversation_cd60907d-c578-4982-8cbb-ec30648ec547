[2025-06-10 14:19:25][MainThread][INFO]: ==================================================
[2025-06-10 14:19:25][MainThread][INFO]: 启动语音识别gRPC服务
[2025-06-10 14:19:25][MainThread][INFO]: ==================================================
[2025-06-10 14:19:25][MainThread][INFO]: 初始化语音识别推理引擎...
[2025-06-10 14:19:25][MainThread][INFO]: 推理模式: funasr
[2025-06-10 14:19:25][MainThread][INFO]: 使用FUNASR模式 - funasr AutoModel (支持更好的断句)
[2025-06-10 14:19:30][MainThread][INFO]: 开始加载funasr语音识别模型，模型路径: ./SenseVoice_model
[2025-06-10 14:19:30][MainThread][INFO]: 使用设备: cuda:0
[2025-06-10 14:19:30][MainThread][INFO]: funasr version: 1.2.6.
[2025-06-10 14:19:30][MainThread][INFO]: Check update of funasr, and it would cost few times. You may disable it by set `disable_update=True` in AutoModel
[2025-06-10 14:19:31][MainThread][INFO]: You are using the latest version of funasr-1.2.6
[2025-06-10 14:19:32][MainThread][INFO]: Loading remote code successfully: ./model.py
[2025-06-10 14:19:40][MainThread][INFO]: Downloading Model from https://www.modelscope.cn to directory: /home/<USER>/.cache/modelscope/hub/models/iic/speech_fsmn_vad_zh-cn-16k-common-pytorch
[2025-06-10 14:19:40][MainThread][ERROR]: 2025-06-10 14:19:40,756 - modelscope - WARNING - Using branch: master as version is unstable, use with caution
[2025-06-10 14:19:41][MainThread][INFO]: funasr模型加载成功，耗时: 10.98秒
[2025-06-10 14:19:41][MainThread][INFO]: 推理引擎加载成功，模式: funasr
[2025-06-10 14:19:41][MainThread][INFO]: 推理引擎信息: funasr AutoModel - 断句效果好，更贴近对话，但需要联网
[2025-06-10 14:19:41][MainThread][INFO]: 语音识别推理引擎初始化成功
[2025-06-10 14:19:41][MainThread][INFO]: 服务名称: speech_recognition
[2025-06-10 14:19:41][MainThread][INFO]: 监听地址: 0.0.0.0:50051
[2025-06-10 14:19:41][MainThread][INFO]: 模型路径: ./SenseVoice_model
[2025-06-10 14:19:41][MainThread][INFO]: 推理设备: cuda:0
[2025-06-10 14:19:41][MainThread][INFO]: 推理模式: funasr
[2025-06-10 14:19:41][MainThread][INFO]: 最大工作线程: 20
[2025-06-10 14:19:41][MainThread][INFO]: 最大消息大小: 50.0MB
[2025-06-10 14:19:41][MainThread][INFO]: 语音识别gRPC服务启动成功
[2025-06-10 14:20:20][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: f7dfad98-fb8c-42a4-be2b-98d3474a2081
[2025-06-10 14:20:20][ThreadPoolExecutor-0_0][INFO]: 任务 f7dfad98-fb8c-42a4-be2b-98d3474a2081: 音频数量=1, 语言=zh
[2025-06-10 14:20:20][ThreadPoolExecutor-0_0][INFO]: 开始funasr语音识别推理，音频数量: 1, 语言: zh
[2025-06-10 14:20:20][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:20:21][ThreadPoolExecutor-0_0][ERROR]: 100%|[34m##########[0m| 1/1 [00:00<00:00,  2.94it/s]
[2025-06-10 14:20:21][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.021', 'extract_feat': '0.291', 'forward': '0.340', 'batch_size': '1', 'rtf': '0.032'}, : 100%|[34m##########[0m| 1/1 [00:00<00:00,  2.94it/s]
[2025-06-10 14:20:21][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.032: 100%|[34m##########[0m| 1/1 [00:00<00:00,  2.94it/s]
[2025-06-10 14:20:21][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.032: 100%|[34m##########[0m| 1/1 [00:00<00:00,  2.90it/s]
[2025-06-10 14:20:21][ThreadPoolExecutor-0_0][ERROR]: 0%|[31m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:20:21][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/5 [00:00<?, ?it/s]
[2025-06-10 14:20:21][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:20:21][ThreadPoolExecutor-0_0][ERROR]: 100%|[34m##########[0m| 5/5 [00:00<00:00, 22.69it/s]
[2025-06-10 14:20:21][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:20:21][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.000', 'extract_feat': '0.046', 'forward': '0.220', 'batch_size': '5', 'rtf': '0.004'}, : 100%|[34m##########[0m| 5/5 [00:00<00:00, 22.69it/s]
[2025-06-10 14:20:21][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:20:21][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004: 100%|[34m##########[0m| 5/5 [00:00<00:00, 22.69it/s]
[2025-06-10 14:20:21][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:20:21][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004: 100%|[34m##########[0m| 5/5 [00:00<00:00, 21.53it/s]
[2025-06-10 14:20:21][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:20:21][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:20:21][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.000', 'extract_feat': '0.015', 'forward': '0.090', 'batch_size': '1', 'rtf': '0.005'}, : 100%|[34m##########[0m| 1/1 [00:00<00:00, 11.08it/s]
[2025-06-10 14:20:21][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:20:21][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.005: 100%|[34m##########[0m| 1/1 [00:00<00:00, 10.98it/s]
[2025-06-10 14:20:21][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:20:21][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.005: 100%|[34m##########[0m| 1/1 [00:00<00:00, 10.59it/s]
[2025-06-10 14:20:21][ThreadPoolExecutor-0_0][ERROR]: 100%|[31m##########[0m| 1/1 [00:00<00:00,  2.89it/s]
[2025-06-10 14:20:21][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.005, time_speech:  70.471, time_escape: 0.338: 100%|[31m##########[0m| 1/1 [00:00<00:00,  2.89it/s]
[2025-06-10 14:20:21][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.005, time_speech:  70.471, time_escape: 0.338: 100%|[31m##########[0m| 1/1 [00:00<00:00,  2.88it/s]
[2025-06-10 14:20:21][ThreadPoolExecutor-0_0][INFO]: funasr推理原始结果: <|zh|><|ANGRY|><|Speech|><|woitn|>试错的过程很简单而且特别是今天报名仓雪卡的同学你们可以 <|zh|><|ANGRY|><|Speech|><|woitn|>听到后面的有专门的活动课他会大大降低你的试错成本其实你也可以过来听课为什么你自己写嘛我先今天写五个点我就试试试验一下反正这五个点不行我再写五个点这试再不行那再写五个点嘛 <|zh|><|ANGRY|><|Speech|><|woitn|>你总会所谓的活动搭神和所谓的高手都是只有一个把所有的错所有的坑全部趟一遍留下正确的你就是所谓的搭神 <|zh|><|ANGRY|><|Speech|><|woitn|>明白吗所以说关于活动通过这一块我只送给你们四个字啊换位思考如果说你要想降低你的试错成本今天来这里你们就是对的 <|zh|><|ANGRY|><|Speech|><|woitn|>因为有畅畅血卡这个机会所以说关于活动过于不过这个问题或者活动很难通过这个话题呃如果真的要坐下来聊的话要聊一天 <|zh|><|HAPPY|><|Speech|><|woitn|>但是我觉得我刚才说的四个字足够好谢谢好非常感谢那个三茂老师的回答啊三茂老师说我们在整个店铺的这个活动当中我们要学会换位思考其实
[2025-06-10 14:20:21][ThreadPoolExecutor-0_0][INFO]: funasr处理后结果: 试错的过程很简单而且特别是今天报名仓雪卡的同学你们可以听到后面的有专门的活动课他会大大降低你的试错成本其实你也可以过来听课为什么你自己写嘛我先今天写五个点我就试试试验一下反正这五个点不行我再写五个点这试再不行那再写五个点嘛你总会所谓的活动搭神和所谓的高手都是只有一个把所有的错所有的坑全部趟一遍留下正确的你就是所谓的搭神明白吗所以说关于活动通过这一块我只送给你们四个字啊换位思考如果说你要想降低你的试错成本今天来这里你们就是对的因为有畅畅血卡这个机会所以说关于活动过于不过这个问题或者活动很难通过这个话题呃如果真的要坐下来聊的话要聊一天😡但是我觉得我刚才说的四个字足够好谢谢好非常感谢那个三茂老师的回答啊三茂老师说我们在整个店铺的这个活动当中我们要学会换位思考其实😊
[2025-06-10 14:20:21][ThreadPoolExecutor-0_0][INFO]: funasr语音识别完成 - 总耗时: 0.703秒 | 验证: 0.000秒 | 文件准备: 0.005秒 | 模型推理: 0.697秒 | 清理: 0.000秒
[2025-06-10 14:20:21][ThreadPoolExecutor-0_0][INFO]: 任务 f7dfad98-fb8c-42a4-be2b-98d3474a2081 完成，处理时间: 0.71秒，结果数量: 1 

[2025-06-10 14:21:43][MainThread][INFO]: 收到停止信号，正在关闭服务...
[2025-06-10 14:21:43][MainThread][INFO]: 服务已停止
[2025-06-10 14:24:31][MainThread][INFO]: ==================================================
[2025-06-10 14:24:31][MainThread][INFO]: 启动语音识别gRPC服务
[2025-06-10 14:24:31][MainThread][INFO]: ==================================================
[2025-06-10 14:24:31][MainThread][INFO]: 初始化语音识别推理引擎...
[2025-06-10 14:24:31][MainThread][INFO]: 推理模式: funasr
[2025-06-10 14:24:31][MainThread][INFO]: 使用FUNASR模式 - funasr AutoModel (支持更好的断句)
[2025-06-10 14:24:34][MainThread][INFO]: 开始加载funasr语音识别模型，模型路径: ./SenseVoice_model
[2025-06-10 14:24:34][MainThread][INFO]: 使用设备: cuda:0
[2025-06-10 14:24:34][MainThread][INFO]: funasr version: 1.2.6.
[2025-06-10 14:24:34][MainThread][INFO]: Check update of funasr, and it would cost few times. You may disable it by set `disable_update=True` in AutoModel
[2025-06-10 14:24:41][MainThread][INFO]: You are using the latest version of funasr-1.2.6
[2025-06-10 14:24:41][MainThread][INFO]: Loading remote code successfully: ./model.py
[2025-06-10 14:24:48][MainThread][INFO]: Downloading Model from https://www.modelscope.cn to directory: /home/<USER>/.cache/modelscope/hub/models/iic/speech_fsmn_vad_zh-cn-16k-common-pytorch
[2025-06-10 14:24:48][MainThread][ERROR]: 2025-06-10 14:24:48,380 - modelscope - WARNING - Using branch: master as version is unstable, use with caution
[2025-06-10 14:24:48][MainThread][INFO]: funasr模型加载成功，耗时: 13.83秒
[2025-06-10 14:24:48][MainThread][INFO]: 推理引擎加载成功，模式: funasr
[2025-06-10 14:24:48][MainThread][INFO]: 推理引擎信息: funasr AutoModel - 断句效果好，更贴近对话，但需要联网
[2025-06-10 14:24:48][MainThread][INFO]: 语音识别推理引擎初始化成功
[2025-06-10 14:24:48][MainThread][INFO]: 服务名称: speech_recognition
[2025-06-10 14:24:48][MainThread][INFO]: 监听地址: 0.0.0.0:50051
[2025-06-10 14:24:48][MainThread][INFO]: 模型路径: ./SenseVoice_model
[2025-06-10 14:24:48][MainThread][INFO]: 推理设备: cuda:0
[2025-06-10 14:24:48][MainThread][INFO]: 推理模式: funasr
[2025-06-10 14:24:48][MainThread][INFO]: 最大工作线程: 20
[2025-06-10 14:24:48][MainThread][INFO]: 最大消息大小: 50.0MB
[2025-06-10 14:24:48][MainThread][INFO]: 语音识别gRPC服务启动成功
[2025-06-10 14:25:42][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: 64645faf-f440-42b5-a2bb-52460308460b
[2025-06-10 14:25:42][ThreadPoolExecutor-0_0][INFO]: 任务 64645faf-f440-42b5-a2bb-52460308460b: 音频数量=1, 语言=zh
[2025-06-10 14:25:42][ThreadPoolExecutor-0_0][INFO]: 开始funasr语音识别推理，音频数量: 1, 语言: zh
[2025-06-10 14:25:42][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:25:42][ThreadPoolExecutor-0_0][ERROR]: 100%|[34m##########[0m| 1/1 [00:00<00:00,  3.29it/s]
[2025-06-10 14:25:42][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.019', 'extract_feat': '0.256', 'forward': '0.304', 'batch_size': '1', 'rtf': '0.029'}, : 100%|[34m##########[0m| 1/1 [00:00<00:00,  3.29it/s]
[2025-06-10 14:25:42][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.029: 100%|[34m##########[0m| 1/1 [00:00<00:00,  3.29it/s]
[2025-06-10 14:25:42][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.029: 100%|[34m##########[0m| 1/1 [00:00<00:00,  3.25it/s]
[2025-06-10 14:25:42][ThreadPoolExecutor-0_0][ERROR]: 0%|[31m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:25:42][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/5 [00:00<?, ?it/s]
[2025-06-10 14:25:42][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:25:42][ThreadPoolExecutor-0_0][ERROR]: 100%|[34m##########[0m| 5/5 [00:00<00:00, 26.03it/s]
[2025-06-10 14:25:42][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:25:42][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.000', 'extract_feat': '0.060', 'forward': '0.192', 'batch_size': '5', 'rtf': '0.004'}, : 100%|[34m##########[0m| 5/5 [00:00<00:00, 26.03it/s]
[2025-06-10 14:25:42][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:25:42][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004: 100%|[34m##########[0m| 5/5 [00:00<00:00, 26.03it/s]
[2025-06-10 14:25:42][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:25:42][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004: 100%|[34m##########[0m| 5/5 [00:00<00:00, 24.72it/s]
[2025-06-10 14:25:42][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:25:42][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:25:42][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.000', 'extract_feat': '0.014', 'forward': '0.067', 'batch_size': '1', 'rtf': '0.004'}, : 100%|[34m##########[0m| 1/1 [00:00<00:00, 15.00it/s]
[2025-06-10 14:25:42][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:25:42][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004: 100%|[34m##########[0m| 1/1 [00:00<00:00, 14.88it/s]
[2025-06-10 14:25:42][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:25:42][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004: 100%|[34m##########[0m| 1/1 [00:00<00:00, 14.25it/s]
[2025-06-10 14:25:42][ThreadPoolExecutor-0_0][ERROR]: 100%|[31m##########[0m| 1/1 [00:00<00:00,  3.41it/s]
[2025-06-10 14:25:42][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004, time_speech:  70.471, time_escape: 0.285: 100%|[31m##########[0m| 1/1 [00:00<00:00,  3.41it/s]
[2025-06-10 14:25:42][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004, time_speech:  70.471, time_escape: 0.285: 100%|[31m##########[0m| 1/1 [00:00<00:00,  3.40it/s]
[2025-06-10 14:25:42][ThreadPoolExecutor-0_0][INFO]: funasr推理原始结果: <|zh|><|ANGRY|><|Speech|><|woitn|>试错的过程很简单而且特别是今天报名仓雪卡的同学你们可以 <|zh|><|ANGRY|><|Speech|><|woitn|>听到后面的有专门的活动课他会大大降低你的试错成本其实你也可以过来听课为什么你自己写嘛我先今天写五个点我就试试试验一下反正这五个点不行我再写五个点这试再不行那再写五个点嘛 <|zh|><|ANGRY|><|Speech|><|woitn|>你总会所谓的活动搭神和所谓的高手都是只有一个把所有的错所有的坑全部趟一遍留下正确的你就是所谓的搭神 <|zh|><|ANGRY|><|Speech|><|woitn|>明白吗所以说关于活动通过这一块我只送给你们四个字啊换位思考如果说你要想降低你的试错成本今天来这里你们就是对的 <|zh|><|ANGRY|><|Speech|><|woitn|>因为有畅畅血卡这个机会所以说关于活动过于不过这个问题或者活动很难通过这个话题呃如果真的要坐下来聊的话要聊一天 <|zh|><|HAPPY|><|Speech|><|woitn|>但是我觉得我刚才说的四个字足够好谢谢好非常感谢那个三茂老师的回答啊三茂老师说我们在整个店铺的这个活动当中我们要学会换位思考其实
[2025-06-10 14:25:42][ThreadPoolExecutor-0_0][INFO]: funasr处理后结果: 试错的过程很简单而且特别是今天报名仓雪卡的同学你们可以听到后面的有专门的活动课他会大大降低你的试错成本其实你也可以过来听课为什么你自己写嘛我先今天写五个点我就试试试验一下反正这五个点不行我再写五个点这试再不行那再写五个点嘛你总会所谓的活动搭神和所谓的高手都是只有一个把所有的错所有的坑全部趟一遍留下正确的你就是所谓的搭神明白吗所以说关于活动通过这一块我只送给你们四个字啊换位思考如果说你要想降低你的试错成本今天来这里你们就是对的因为有畅畅血卡这个机会所以说关于活动过于不过这个问题或者活动很难通过这个话题呃如果真的要坐下来聊的话要聊一天😡但是我觉得我刚才说的四个字足够好谢谢好非常感谢那个三茂老师的回答啊三茂老师说我们在整个店铺的这个活动当中我们要学会换位思考其实😊
[2025-06-10 14:25:42][ThreadPoolExecutor-0_0][INFO]: funasr语音识别完成 - 总耗时: 0.607秒 | 验证: 0.000秒 | 文件准备: 0.002秒 | 模型推理: 0.605秒 | 清理: 0.000秒
[2025-06-10 14:25:42][ThreadPoolExecutor-0_0][INFO]: 任务 64645faf-f440-42b5-a2bb-52460308460b 完成，处理时间: 0.61秒，结果数量: 1 

[2025-06-10 14:28:51][MainThread][INFO]: 收到停止信号，正在关闭服务...
[2025-06-10 14:28:51][MainThread][INFO]: 服务已停止
[2025-06-10 14:28:52][MainThread][INFO]: ==================================================
[2025-06-10 14:28:52][MainThread][INFO]: 启动语音识别gRPC服务
[2025-06-10 14:28:52][MainThread][INFO]: ==================================================
[2025-06-10 14:28:52][MainThread][INFO]: 初始化语音识别推理引擎...
[2025-06-10 14:28:52][MainThread][INFO]: 推理模式: funasr
[2025-06-10 14:28:52][MainThread][INFO]: 使用FUNASR模式 - funasr AutoModel (支持更好的断句)
[2025-06-10 14:28:56][MainThread][INFO]: 开始加载funasr语音识别模型，模型路径: ./SenseVoice_model
[2025-06-10 14:28:56][MainThread][INFO]: 使用设备: cuda:0
[2025-06-10 14:28:56][MainThread][INFO]: funasr version: 1.2.6.
[2025-06-10 14:28:56][MainThread][INFO]: Check update of funasr, and it would cost few times. You may disable it by set `disable_update=True` in AutoModel
[2025-06-10 14:29:05][MainThread][INFO]: You are using the latest version of funasr-1.2.6
[2025-06-10 14:29:05][MainThread][INFO]: Loading remote code successfully: ./model.py
[2025-06-10 14:29:13][MainThread][INFO]: Downloading Model from https://www.modelscope.cn to directory: /home/<USER>/.cache/modelscope/hub/models/iic/speech_fsmn_vad_zh-cn-16k-common-pytorch
[2025-06-10 14:29:14][MainThread][ERROR]: 2025-06-10 14:29:14,037 - modelscope - WARNING - Using branch: master as version is unstable, use with caution
[2025-06-10 14:29:14][MainThread][INFO]: funasr模型加载成功，耗时: 17.49秒
[2025-06-10 14:29:14][MainThread][INFO]: 推理引擎加载成功，模式: funasr
[2025-06-10 14:29:14][MainThread][INFO]: 推理引擎信息: funasr AutoModel - 断句效果好，更贴近对话，但需要联网
[2025-06-10 14:29:14][MainThread][INFO]: 语音识别推理引擎初始化成功
[2025-06-10 14:29:14][MainThread][INFO]: 服务名称: speech_recognition
[2025-06-10 14:29:14][MainThread][INFO]: 监听地址: 0.0.0.0:50051
[2025-06-10 14:29:14][MainThread][INFO]: 模型路径: ./SenseVoice_model
[2025-06-10 14:29:14][MainThread][INFO]: 推理设备: cuda:0
[2025-06-10 14:29:14][MainThread][INFO]: 推理模式: funasr
[2025-06-10 14:29:14][MainThread][INFO]: 最大工作线程: 20
[2025-06-10 14:29:14][MainThread][INFO]: 最大消息大小: 50.0MB
[2025-06-10 14:29:14][MainThread][INFO]: 语音识别gRPC服务启动成功
[2025-06-10 14:29:22][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: 695d9696-e85f-42d4-b550-4c31ab965b5b
[2025-06-10 14:29:22][ThreadPoolExecutor-0_0][INFO]: 任务 695d9696-e85f-42d4-b550-4c31ab965b5b: 音频数量=1, 语言=zh
[2025-06-10 14:29:22][ThreadPoolExecutor-0_0][INFO]: 开始funasr语音识别推理，音频数量: 1, 语言: zh
[2025-06-10 14:29:22][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:29:22][ThreadPoolExecutor-0_0][ERROR]: 100%|[34m##########[0m| 1/1 [00:00<00:00,  3.11it/s]
[2025-06-10 14:29:22][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.019', 'extract_feat': '0.273', 'forward': '0.321', 'batch_size': '1', 'rtf': '0.031'}, : 100%|[34m##########[0m| 1/1 [00:00<00:00,  3.11it/s]
[2025-06-10 14:29:22][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.031: 100%|[34m##########[0m| 1/1 [00:00<00:00,  3.11it/s]
[2025-06-10 14:29:22][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.031: 100%|[34m##########[0m| 1/1 [00:00<00:00,  3.07it/s]
[2025-06-10 14:29:22][ThreadPoolExecutor-0_0][ERROR]: 0%|[31m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:29:22][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/5 [00:00<?, ?it/s]
[2025-06-10 14:29:22][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:29:22][ThreadPoolExecutor-0_0][ERROR]: 100%|[34m##########[0m| 5/5 [00:00<00:00, 27.00it/s]
[2025-06-10 14:29:22][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:29:22][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.000', 'extract_feat': '0.052', 'forward': '0.185', 'batch_size': '5', 'rtf': '0.003'}, : 100%|[34m##########[0m| 5/5 [00:00<00:00, 27.00it/s]
[2025-06-10 14:29:22][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:29:22][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.003: 100%|[34m##########[0m| 5/5 [00:00<00:00, 27.00it/s]
[2025-06-10 14:29:22][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:29:22][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.003: 100%|[34m##########[0m| 5/5 [00:00<00:00, 25.65it/s]
[2025-06-10 14:29:22][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:29:22][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:29:23][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.000', 'extract_feat': '0.015', 'forward': '0.086', 'batch_size': '1', 'rtf': '0.005'}, : 100%|[34m##########[0m| 1/1 [00:00<00:00, 11.64it/s]
[2025-06-10 14:29:23][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:29:23][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.005: 100%|[34m##########[0m| 1/1 [00:00<00:00, 11.55it/s]
[2025-06-10 14:29:23][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:29:23][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.005: 100%|[34m##########[0m| 1/1 [00:00<00:00, 11.14it/s]
[2025-06-10 14:29:23][ThreadPoolExecutor-0_0][ERROR]: 100%|[31m##########[0m| 1/1 [00:00<00:00,  3.27it/s]
[2025-06-10 14:29:23][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004, time_speech:  70.471, time_escape: 0.298: 100%|[31m##########[0m| 1/1 [00:00<00:00,  3.27it/s]
[2025-06-10 14:29:23][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004, time_speech:  70.471, time_escape: 0.298: 100%|[31m##########[0m| 1/1 [00:00<00:00,  3.26it/s]
[2025-06-10 14:29:23][ThreadPoolExecutor-0_0][INFO]: 处理文本: 试错的过程很简单而且特别是今天报名仓雪卡的同学你们可以听到后面的有专门的活动课他会大大降低你的试错成本其实你也可以过来听课为什么你自己写嘛我先今天写五个点我就试试试验一下反正这五个点不行我再写五个点这试再不行那再写五个点嘛你总会所谓的活动搭神和所谓的高手都是只有一个把所有的错所有的坑全部趟一遍留下正确的你就是所谓的搭神明白吗所以说关于活动通过这一块我只送给你们四个字啊换位思考如果说你要想降低你的试错成本今天来这里你们就是对的因为有畅畅血卡这个机会所以说关于活动过于不过这个问题或者活动很难通过这个话题呃如果真的要坐下来聊的话要聊一天😡但是我觉得我刚才说的四个字足够好谢谢好非常感谢那个三茂老师的回答啊三茂老师说我们在整个店铺的这个活动当中我们要学会换位思考其实😊
[2025-06-10 14:29:23][ThreadPoolExecutor-0_0][INFO]: funasr推理原始结果: <|zh|><|ANGRY|><|Speech|><|woitn|>试错的过程很简单而且特别是今天报名仓雪卡的同学你们可以 <|zh|><|ANGRY|><|Speech|><|woitn|>听到后面的有专门的活动课他会大大降低你的试错成本其实你也可以过来听课为什么你自己写嘛我先今天写五个点我就试试试验一下反正这五个点不行我再写五个点这试再不行那再写五个点嘛 <|zh|><|ANGRY|><|Speech|><|woitn|>你总会所谓的活动搭神和所谓的高手都是只有一个把所有的错所有的坑全部趟一遍留下正确的你就是所谓的搭神 <|zh|><|ANGRY|><|Speech|><|woitn|>明白吗所以说关于活动通过这一块我只送给你们四个字啊换位思考如果说你要想降低你的试错成本今天来这里你们就是对的 <|zh|><|ANGRY|><|Speech|><|woitn|>因为有畅畅血卡这个机会所以说关于活动过于不过这个问题或者活动很难通过这个话题呃如果真的要坐下来聊的话要聊一天 <|zh|><|HAPPY|><|Speech|><|woitn|>但是我觉得我刚才说的四个字足够好谢谢好非常感谢那个三茂老师的回答啊三茂老师说我们在整个店铺的这个活动当中我们要学会换位思考其实
[2025-06-10 14:29:23][ThreadPoolExecutor-0_0][INFO]: funasr处理后结果: 试错的过程很简单而且特别是今天报名仓雪卡的同学你们可以听到后面的有专门的活动课他会大大降低你的试错成本其实你也可以过来听课为什么你自己写嘛我先今天写五个点我就试试试验一下反正这五个点不行我再写五个点这试再不行那再写五个点嘛你总会所谓的活动搭神和所谓的高手都是只有一个把所有的错所有的坑全部趟一遍留下正确的你就是所谓的搭神明白吗所以说关于活动通过这一块我只送给你们四个字啊换位思考如果说你要想降低你的试错成本今天来这里你们就是对的因为有畅畅血卡这个机会所以说关于活动过于不过这个问题或者活动很难通过这个话题呃如果真的要坐下来聊的话要聊一天😡但是我觉得我刚才说的四个字足够好谢谢好非常感谢那个三茂老师的回答啊三茂老师说我们在整个店铺的这个活动当中我们要学会换位思考其实😊
[2025-06-10 14:29:23][ThreadPoolExecutor-0_0][INFO]: funasr语音识别完成 - 总耗时: 0.638秒 | 验证: 0.000秒 | 文件准备: 0.002秒 | 模型推理: 0.636秒 | 清理: 0.000秒
[2025-06-10 14:29:23][ThreadPoolExecutor-0_0][INFO]: 任务 695d9696-e85f-42d4-b550-4c31ab965b5b 完成，处理时间: 0.64秒，结果数量: 1 

[2025-06-10 14:29:36][MainThread][INFO]: 收到停止信号，正在关闭服务...
[2025-06-10 14:29:36][MainThread][INFO]: 服务已停止
[2025-06-10 14:37:19][MainThread][INFO]: ==================================================
[2025-06-10 14:37:19][MainThread][INFO]: 启动语音识别gRPC服务
[2025-06-10 14:37:19][MainThread][INFO]: ==================================================
[2025-06-10 14:37:19][MainThread][INFO]: 初始化语音识别推理引擎...
[2025-06-10 14:37:19][MainThread][INFO]: 推理模式: funasr
[2025-06-10 14:37:19][MainThread][INFO]: 使用FUNASR模式 - funasr AutoModel (支持更好的断句)
[2025-06-10 14:37:22][MainThread][INFO]: 开始加载funasr语音识别模型，模型路径: ./SenseVoice_model
[2025-06-10 14:37:22][MainThread][INFO]: 使用设备: cuda:0
[2025-06-10 14:37:22][MainThread][INFO]: funasr version: 1.2.6.
[2025-06-10 14:37:22][MainThread][INFO]: Check update of funasr, and it would cost few times. You may disable it by set `disable_update=True` in AutoModel
[2025-06-10 14:37:28][MainThread][INFO]: You are using the latest version of funasr-1.2.6
[2025-06-10 14:37:28][MainThread][INFO]: Loading remote code successfully: ./model.py
[2025-06-10 14:37:35][MainThread][INFO]: Downloading Model from https://www.modelscope.cn to directory: /home/<USER>/.cache/modelscope/hub/models/iic/speech_fsmn_vad_zh-cn-16k-common-pytorch
[2025-06-10 14:37:35][MainThread][ERROR]: 2025-06-10 14:37:35,358 - modelscope - WARNING - Using branch: master as version is unstable, use with caution
[2025-06-10 14:37:35][MainThread][INFO]: funasr模型加载成功，耗时: 12.62秒
[2025-06-10 14:37:35][MainThread][INFO]: 推理引擎加载成功，模式: funasr
[2025-06-10 14:37:35][MainThread][INFO]: 推理引擎信息: funasr AutoModel - 断句效果好，更贴近对话，但需要联网
[2025-06-10 14:37:35][MainThread][INFO]: 语音识别推理引擎初始化成功
[2025-06-10 14:37:35][MainThread][INFO]: 服务名称: speech_recognition
[2025-06-10 14:37:35][MainThread][INFO]: 监听地址: 0.0.0.0:50051
[2025-06-10 14:37:35][MainThread][INFO]: 模型路径: ./SenseVoice_model
[2025-06-10 14:37:35][MainThread][INFO]: 推理设备: cuda:0
[2025-06-10 14:37:35][MainThread][INFO]: 推理模式: funasr
[2025-06-10 14:37:35][MainThread][INFO]: 最大工作线程: 20
[2025-06-10 14:37:35][MainThread][INFO]: 最大消息大小: 50.0MB
[2025-06-10 14:37:35][MainThread][INFO]: 语音识别gRPC服务启动成功
[2025-06-10 14:38:28][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: 1850a5ad-e485-43fe-ab86-a35a734694cf
[2025-06-10 14:38:28][ThreadPoolExecutor-0_0][INFO]: 任务 1850a5ad-e485-43fe-ab86-a35a734694cf: 音频数量=1, 语言=zh
[2025-06-10 14:38:28][ThreadPoolExecutor-0_0][INFO]: 开始funasr语音识别推理，音频数量: 1, 语言: zh
[2025-06-10 14:38:28][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:38:29][ThreadPoolExecutor-0_0][ERROR]: 100%|[34m##########[0m| 1/1 [00:00<00:00,  3.28it/s]
[2025-06-10 14:38:29][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.032', 'extract_feat': '0.238', 'forward': '0.305', 'batch_size': '1', 'rtf': '0.029'}, : 100%|[34m##########[0m| 1/1 [00:00<00:00,  3.28it/s]
[2025-06-10 14:38:29][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.029: 100%|[34m##########[0m| 1/1 [00:00<00:00,  3.28it/s]
[2025-06-10 14:38:29][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.029: 100%|[34m##########[0m| 1/1 [00:00<00:00,  3.23it/s]
[2025-06-10 14:38:29][ThreadPoolExecutor-0_0][ERROR]: 0%|[31m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:38:29][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/5 [00:00<?, ?it/s]
[2025-06-10 14:38:29][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:38:29][ThreadPoolExecutor-0_0][ERROR]: 100%|[34m##########[0m| 5/5 [00:00<00:00, 23.32it/s]
[2025-06-10 14:38:29][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:38:29][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.000', 'extract_feat': '0.043', 'forward': '0.214', 'batch_size': '5', 'rtf': '0.004'}, : 100%|[34m##########[0m| 5/5 [00:00<00:00, 23.32it/s]
[2025-06-10 14:38:29][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:38:29][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004: 100%|[34m##########[0m| 5/5 [00:00<00:00, 23.32it/s]
[2025-06-10 14:38:29][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:38:29][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004: 100%|[34m##########[0m| 5/5 [00:00<00:00, 22.20it/s]
[2025-06-10 14:38:29][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:38:29][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:38:29][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.000', 'extract_feat': '0.012', 'forward': '0.075', 'batch_size': '1', 'rtf': '0.004'}, : 100%|[34m##########[0m| 1/1 [00:00<00:00, 13.31it/s]
[2025-06-10 14:38:29][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:38:29][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004: 100%|[34m##########[0m| 1/1 [00:00<00:00, 13.19it/s]
[2025-06-10 14:38:29][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:38:29][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004: 100%|[34m##########[0m| 1/1 [00:00<00:00, 12.59it/s]
[2025-06-10 14:38:29][ThreadPoolExecutor-0_0][ERROR]: 100%|[31m##########[0m| 1/1 [00:00<00:00,  3.11it/s]
[2025-06-10 14:38:29][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004, time_speech:  70.471, time_escape: 0.313: 100%|[31m##########[0m| 1/1 [00:00<00:00,  3.11it/s]
[2025-06-10 14:38:29][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004, time_speech:  70.471, time_escape: 0.313: 100%|[31m##########[0m| 1/1 [00:00<00:00,  3.10it/s]
[2025-06-10 14:38:29][ThreadPoolExecutor-0_0][INFO]: 处理文本: 试错的过程很简单而且特别是今天报名仓雪卡的同学你们可以听到后面的有专门的活动课他会大大降低你的试错成本其实你也可以过来听课为什么你自己写嘛我先今天写五个点我就试试试验一下反正这五个点不行我再写五个点这试再不行那再写五个点嘛你总会所谓的活动搭神和所谓的高手都是只有一个把所有的错所有的坑全部趟一遍留下正确的你就是所谓的搭神明白吗所以说关于活动通过这一块我只送给你们四个字啊换位思考如果说你要想降低你的试错成本今天来这里你们就是对的因为有畅畅血卡这个机会所以说关于活动过于不过这个问题或者活动很难通过这个话题呃如果真的要坐下来聊的话要聊一天😡但是我觉得我刚才说的四个字足够好谢谢好非常感谢那个三茂老师的回答啊三茂老师说我们在整个店铺的这个活动当中我们要学会换位思考其实😊
[2025-06-10 14:38:29][ThreadPoolExecutor-0_0][INFO]: funasr推理原始结果: <|zh|><|ANGRY|><|Speech|><|woitn|>试错的过程很简单而且特别是今天报名仓雪卡的同学你们可以 <|zh|><|ANGRY|><|Speech|><|woitn|>听到后面的有专门的活动课他会大大降低你的试错成本其实你也可以过来听课为什么你自己写嘛我先今天写五个点我就试试试验一下反正这五个点不行我再写五个点这试再不行那再写五个点嘛 <|zh|><|ANGRY|><|Speech|><|woitn|>你总会所谓的活动搭神和所谓的高手都是只有一个把所有的错所有的坑全部趟一遍留下正确的你就是所谓的搭神 <|zh|><|ANGRY|><|Speech|><|woitn|>明白吗所以说关于活动通过这一块我只送给你们四个字啊换位思考如果说你要想降低你的试错成本今天来这里你们就是对的 <|zh|><|ANGRY|><|Speech|><|woitn|>因为有畅畅血卡这个机会所以说关于活动过于不过这个问题或者活动很难通过这个话题呃如果真的要坐下来聊的话要聊一天 <|zh|><|HAPPY|><|Speech|><|woitn|>但是我觉得我刚才说的四个字足够好谢谢好非常感谢那个三茂老师的回答啊三茂老师说我们在整个店铺的这个活动当中我们要学会换位思考其实
[2025-06-10 14:38:29][ThreadPoolExecutor-0_0][INFO]: funasr处理后结果: 试错的过程很简单而且特别是今天报名仓雪卡的同学你们可以听到后面的有专门的活动课他会大大降低你的试错成本其实你也可以过来听课为什么你自己写嘛我先今天写五个点我就试试试验一下反正这五个点不行我再写五个点这试再不行那再写五个点嘛你总会所谓的活动搭神和所谓的高手都是只有一个把所有的错所有的坑全部趟一遍留下正确的你就是所谓的搭神明白吗所以说关于活动通过这一块我只送给你们四个字啊换位思考如果说你要想降低你的试错成本今天来这里你们就是对的因为有畅畅血卡这个机会所以说关于活动过于不过这个问题或者活动很难通过这个话题呃如果真的要坐下来聊的话要聊一天😡但是我觉得我刚才说的四个字足够好谢谢好非常感谢那个三茂老师的回答啊三茂老师说我们在整个店铺的这个活动当中我们要学会换位思考其实😊
[2025-06-10 14:38:29][ThreadPoolExecutor-0_0][INFO]: funasr语音识别完成 - 总耗时: 0.640秒 | 验证: 0.000秒 | 文件准备: 0.003秒 | 模型推理: 0.637秒 | 清理: 0.000秒
[2025-06-10 14:38:29][ThreadPoolExecutor-0_0][INFO]: 任务 1850a5ad-e485-43fe-ab86-a35a734694cf 完成，处理时间: 0.64秒，结果数量: 1 

[2025-06-10 14:40:08][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: 478a57a7-8fe3-402d-aa27-93743fdcea0b
[2025-06-10 14:40:08][ThreadPoolExecutor-0_0][INFO]: 任务 478a57a7-8fe3-402d-aa27-93743fdcea0b: 音频数量=1, 语言=zh
[2025-06-10 14:40:08][ThreadPoolExecutor-0_0][INFO]: 开始funasr语音识别推理，音频数量: 1, 语言: zh
[2025-06-10 14:40:08][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:40:08][ThreadPoolExecutor-0_0][ERROR]: 100%|[34m##########[0m| 1/1 [00:00<00:00,  3.02it/s]
[2025-06-10 14:40:08][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.008', 'extract_feat': '0.297', 'forward': '0.331', 'batch_size': '1', 'rtf': '0.032'}, : 100%|[34m##########[0m| 1/1 [00:00<00:00,  3.02it/s]
[2025-06-10 14:40:08][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.032: 100%|[34m##########[0m| 1/1 [00:00<00:00,  3.02it/s]
[2025-06-10 14:40:08][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.032: 100%|[34m##########[0m| 1/1 [00:00<00:00,  2.98it/s]
[2025-06-10 14:40:08][ThreadPoolExecutor-0_0][ERROR]: 0%|[31m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:40:08][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/5 [00:00<?, ?it/s]
[2025-06-10 14:40:08][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:40:08][ThreadPoolExecutor-0_0][ERROR]: 100%|[34m##########[0m| 5/5 [00:00<00:00, 23.88it/s]
[2025-06-10 14:40:08][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:40:08][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.000', 'extract_feat': '0.042', 'forward': '0.209', 'batch_size': '5', 'rtf': '0.004'}, : 100%|[34m##########[0m| 5/5 [00:00<00:00, 23.88it/s]
[2025-06-10 14:40:08][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:40:08][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004: 100%|[34m##########[0m| 5/5 [00:00<00:00, 23.88it/s]
[2025-06-10 14:40:08][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:40:08][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004: 100%|[34m##########[0m| 5/5 [00:00<00:00, 22.78it/s]
[2025-06-10 14:40:08][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:40:08][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:40:08][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.000', 'extract_feat': '0.012', 'forward': '0.080', 'batch_size': '1', 'rtf': '0.005'}, : 100%|[34m##########[0m| 1/1 [00:00<00:00, 12.52it/s]
[2025-06-10 14:40:08][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:40:08][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.005: 100%|[34m##########[0m| 1/1 [00:00<00:00, 12.41it/s]
[2025-06-10 14:40:08][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:40:08][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.005: 100%|[34m##########[0m| 1/1 [00:00<00:00, 11.72it/s]
[2025-06-10 14:40:08][ThreadPoolExecutor-0_0][ERROR]: 100%|[31m##########[0m| 1/1 [00:00<00:00,  3.07it/s]
[2025-06-10 14:40:08][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004, time_speech:  70.471, time_escape: 0.316: 100%|[31m##########[0m| 1/1 [00:00<00:00,  3.07it/s]
[2025-06-10 14:40:08][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004, time_speech:  70.471, time_escape: 0.316: 100%|[31m##########[0m| 1/1 [00:00<00:00,  3.07it/s]
[2025-06-10 14:40:08][ThreadPoolExecutor-0_0][INFO]: 处理文本: 试错的过程很简单而，且特别是今天报名仓雪卡的同学，你们可以。听到后面的有专门的活动课，他会大大降低你的试错成本。其实你也可以过来听课，为什么你自己写嘛？我先今天写5个点，我就试试试验一下，反正这5个点不行，我再写5个点，这试再不行，那再写5个点嘛。你总会所谓的活动搭神和所谓的高手，都是只有一个把所有的错，所有的坑全部趟一遍，留下正确的你就是所谓的搭神。明白吗？所以说关于活动通过这块，我只送给你们四个字啊，换位思考。如果说你要想降低你的试错成本，今天来这里你们就是对的。因为有畅畅血卡这个机会，所以说关于活动过于不过这个问题，或者活动很难通过这个话题呃，如果真的要坐下来聊的话，要聊一天。😡但是我觉得我刚才说的四个字足够。好，谢谢。好，非常感谢那个三茂老师的回答啊。三茂老师说，我们在整个店铺的这个活动当中，我们要学会换位思考。其实。😊
[2025-06-10 14:40:08][ThreadPoolExecutor-0_0][INFO]: funasr推理原始结果: <|zh|><|ANGRY|><|Speech|><|withitn|>试错的过程很简单而，且特别是今天报名仓雪卡的同学，你们可以。 <|zh|><|ANGRY|><|Speech|><|withitn|>听到后面的有专门的活动课，他会大大降低你的试错成本。其实你也可以过来听课，为什么你自己写嘛？我先今天写5个点，我就试试试验一下，反正这5个点不行，我再写5个点，这试再不行，那再写5个点嘛。 <|zh|><|ANGRY|><|Speech|><|withitn|>你总会所谓的活动搭神和所谓的高手，都是只有一个把所有的错，所有的坑全部趟一遍，留下正确的你就是所谓的搭神。 <|zh|><|ANGRY|><|Speech|><|withitn|>明白吗？所以说关于活动通过这块，我只送给你们四个字啊，换位思考。如果说你要想降低你的试错成本，今天来这里你们就是对的。 <|zh|><|ANGRY|><|Speech|><|withitn|>因为有畅畅血卡这个机会，所以说关于活动过于不过这个问题，或者活动很难通过这个话题呃，如果真的要坐下来聊的话，要聊一天。 <|zh|><|HAPPY|><|Speech|><|withitn|>但是我觉得我刚才说的四个字足够。好，谢谢。好，非常感谢那个三茂老师的回答啊。三茂老师说，我们在整个店铺的这个活动当中，我们要学会换位思考。其实。
[2025-06-10 14:40:08][ThreadPoolExecutor-0_0][INFO]: funasr处理后结果: 试错的过程很简单而，且特别是今天报名仓雪卡的同学，你们可以。听到后面的有专门的活动课，他会大大降低你的试错成本。其实你也可以过来听课，为什么你自己写嘛？我先今天写5个点，我就试试试验一下，反正这5个点不行，我再写5个点，这试再不行，那再写5个点嘛。你总会所谓的活动搭神和所谓的高手，都是只有一个把所有的错，所有的坑全部趟一遍，留下正确的你就是所谓的搭神。明白吗？所以说关于活动通过这块，我只送给你们四个字啊，换位思考。如果说你要想降低你的试错成本，今天来这里你们就是对的。因为有畅畅血卡这个机会，所以说关于活动过于不过这个问题，或者活动很难通过这个话题呃，如果真的要坐下来聊的话，要聊一天。😡但是我觉得我刚才说的四个字足够。好，谢谢。好，非常感谢那个三茂老师的回答啊。三茂老师说，我们在整个店铺的这个活动当中，我们要学会换位思考。其实。😊
[2025-06-10 14:40:08][ThreadPoolExecutor-0_0][INFO]: funasr语音识别完成 - 总耗时: 0.668秒 | 验证: 0.000秒 | 文件准备: 0.002秒 | 模型推理: 0.665秒 | 清理: 0.001秒
[2025-06-10 14:40:08][ThreadPoolExecutor-0_0][INFO]: 任务 478a57a7-8fe3-402d-aa27-93743fdcea0b 完成，处理时间: 0.67秒，结果数量: 1 

[2025-06-10 14:40:30][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: 88067857-fade-4277-a63b-955463585dba
[2025-06-10 14:40:30][ThreadPoolExecutor-0_0][INFO]: 任务 88067857-fade-4277-a63b-955463585dba: 音频数量=1, 语言=zh
[2025-06-10 14:40:30][ThreadPoolExecutor-0_0][INFO]: 开始funasr语音识别推理，音频数量: 1, 语言: zh
[2025-06-10 14:40:30][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:40:30][ThreadPoolExecutor-0_0][ERROR]: 100%|[34m##########[0m| 1/1 [00:00<00:00,  3.11it/s]
[2025-06-10 14:40:30][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.013', 'extract_feat': '0.270', 'forward': '0.321', 'batch_size': '1', 'rtf': '0.031'}, : 100%|[34m##########[0m| 1/1 [00:00<00:00,  3.11it/s]
[2025-06-10 14:40:30][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.031: 100%|[34m##########[0m| 1/1 [00:00<00:00,  3.11it/s]
[2025-06-10 14:40:30][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.031: 100%|[34m##########[0m| 1/1 [00:00<00:00,  3.06it/s]
[2025-06-10 14:40:30][ThreadPoolExecutor-0_0][ERROR]: 0%|[31m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:40:31][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/5 [00:00<?, ?it/s]
[2025-06-10 14:40:31][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:40:31][ThreadPoolExecutor-0_0][ERROR]: 100%|[34m##########[0m| 5/5 [00:00<00:00, 23.76it/s]
[2025-06-10 14:40:31][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:40:31][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.000', 'extract_feat': '0.040', 'forward': '0.210', 'batch_size': '5', 'rtf': '0.004'}, : 100%|[34m##########[0m| 5/5 [00:00<00:00, 23.76it/s]
[2025-06-10 14:40:31][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:40:31][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004: 100%|[34m##########[0m| 5/5 [00:00<00:00, 23.76it/s]
[2025-06-10 14:40:31][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:40:31][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004: 100%|[34m##########[0m| 5/5 [00:00<00:00, 22.37it/s]
[2025-06-10 14:40:31][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:40:31][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:40:31][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.000', 'extract_feat': '0.015', 'forward': '0.079', 'batch_size': '1', 'rtf': '0.005'}, : 100%|[34m##########[0m| 1/1 [00:00<00:00, 12.57it/s]
[2025-06-10 14:40:31][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:40:31][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.005: 100%|[34m##########[0m| 1/1 [00:00<00:00, 12.45it/s]
[2025-06-10 14:40:31][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:40:31][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.005: 100%|[34m##########[0m| 1/1 [00:00<00:00, 11.93it/s]
[2025-06-10 14:40:31][ThreadPoolExecutor-0_0][ERROR]: 100%|[31m##########[0m| 1/1 [00:00<00:00,  3.03it/s]
[2025-06-10 14:40:31][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.005, time_speech:  70.471, time_escape: 0.319: 100%|[31m##########[0m| 1/1 [00:00<00:00,  3.03it/s]
[2025-06-10 14:40:31][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.005, time_speech:  70.471, time_escape: 0.319: 100%|[31m##########[0m| 1/1 [00:00<00:00,  3.02it/s]
[2025-06-10 14:40:31][ThreadPoolExecutor-0_0][INFO]: 处理文本: 试错的过程很简单而，且特别是今天报名仓雪卡的同学，你们可以。听到后面的有专门的活动课，他会大大降低你的试错成本。其实你也可以过来听课，为什么你自己写嘛？我先今天写5个点，我就试试试验一下，反正这5个点不行，我再写5个点，这试再不行，那再写5个点嘛。你总会所谓的活动搭神和所谓的高手，都是只有一个把所有的错，所有的坑全部趟一遍，留下正确的你就是所谓的搭神。明白吗？所以说关于活动通过这块，我只送给你们四个字啊，换位思考。如果说你要想降低你的试错成本，今天来这里你们就是对的。因为有畅畅血卡这个机会，所以说关于活动过于不过这个问题，或者活动很难通过这个话题呃，如果真的要坐下来聊的话，要聊一天。😡但是我觉得我刚才说的四个字足够。好，谢谢。好，非常感谢那个三茂老师的回答啊。三茂老师说，我们在整个店铺的这个活动当中，我们要学会换位思考。其实。😊
[2025-06-10 14:40:31][ThreadPoolExecutor-0_0][INFO]: funasr推理原始结果: <|zh|><|ANGRY|><|Speech|><|withitn|>试错的过程很简单而，且特别是今天报名仓雪卡的同学，你们可以。 <|zh|><|ANGRY|><|Speech|><|withitn|>听到后面的有专门的活动课，他会大大降低你的试错成本。其实你也可以过来听课，为什么你自己写嘛？我先今天写5个点，我就试试试验一下，反正这5个点不行，我再写5个点，这试再不行，那再写5个点嘛。 <|zh|><|ANGRY|><|Speech|><|withitn|>你总会所谓的活动搭神和所谓的高手，都是只有一个把所有的错，所有的坑全部趟一遍，留下正确的你就是所谓的搭神。 <|zh|><|ANGRY|><|Speech|><|withitn|>明白吗？所以说关于活动通过这块，我只送给你们四个字啊，换位思考。如果说你要想降低你的试错成本，今天来这里你们就是对的。 <|zh|><|ANGRY|><|Speech|><|withitn|>因为有畅畅血卡这个机会，所以说关于活动过于不过这个问题，或者活动很难通过这个话题呃，如果真的要坐下来聊的话，要聊一天。 <|zh|><|HAPPY|><|Speech|><|withitn|>但是我觉得我刚才说的四个字足够。好，谢谢。好，非常感谢那个三茂老师的回答啊。三茂老师说，我们在整个店铺的这个活动当中，我们要学会换位思考。其实。
[2025-06-10 14:40:31][ThreadPoolExecutor-0_0][INFO]: funasr处理后结果: 试错的过程很简单而，且特别是今天报名仓雪卡的同学，你们可以。听到后面的有专门的活动课，他会大大降低你的试错成本。其实你也可以过来听课，为什么你自己写嘛？我先今天写5个点，我就试试试验一下，反正这5个点不行，我再写5个点，这试再不行，那再写5个点嘛。你总会所谓的活动搭神和所谓的高手，都是只有一个把所有的错，所有的坑全部趟一遍，留下正确的你就是所谓的搭神。明白吗？所以说关于活动通过这块，我只送给你们四个字啊，换位思考。如果说你要想降低你的试错成本，今天来这里你们就是对的。因为有畅畅血卡这个机会，所以说关于活动过于不过这个问题，或者活动很难通过这个话题呃，如果真的要坐下来聊的话，要聊一天。😡但是我觉得我刚才说的四个字足够。好，谢谢。好，非常感谢那个三茂老师的回答啊。三茂老师说，我们在整个店铺的这个活动当中，我们要学会换位思考。其实。😊
[2025-06-10 14:40:31][ThreadPoolExecutor-0_0][INFO]: funasr语音识别完成 - 总耗时: 0.664秒 | 验证: 0.000秒 | 文件准备: 0.002秒 | 模型推理: 0.660秒 | 清理: 0.000秒
[2025-06-10 14:40:31][ThreadPoolExecutor-0_0][INFO]: 任务 88067857-fade-4277-a63b-955463585dba 完成，处理时间: 0.67秒，结果数量: 1 

[2025-06-10 14:40:59][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: 74dbc66f-e93e-4c11-a75c-08a17f1caaaa
[2025-06-10 14:40:59][ThreadPoolExecutor-0_0][INFO]: 任务 74dbc66f-e93e-4c11-a75c-08a17f1caaaa: 音频数量=1, 语言=auto
[2025-06-10 14:40:59][ThreadPoolExecutor-0_0][INFO]: 开始funasr语音识别推理，音频数量: 1, 语言: auto
[2025-06-10 14:40:59][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:40:59][ThreadPoolExecutor-0_0][ERROR]: 100%|[34m##########[0m| 1/1 [00:00<00:00,  6.82it/s]
[2025-06-10 14:40:59][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.094', 'extract_feat': '0.007', 'forward': '0.147', 'batch_size': '1', 'rtf': '0.011'}, : 100%|[34m##########[0m| 1/1 [00:00<00:00,  6.82it/s]
[2025-06-10 14:40:59][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.011: 100%|[34m##########[0m| 1/1 [00:00<00:00,  6.82it/s]
[2025-06-10 14:40:59][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.011: 100%|[34m##########[0m| 1/1 [00:00<00:00,  6.73it/s]
[2025-06-10 14:40:59][ThreadPoolExecutor-0_0][ERROR]: 0%|[31m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:40:59][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:40:59][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:40:59][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.000', 'extract_feat': '0.015', 'forward': '0.070', 'batch_size': '1', 'rtf': '0.006'}, : 100%|[34m##########[0m| 1/1 [00:00<00:00, 14.28it/s]
[2025-06-10 14:40:59][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:40:59][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.006: 100%|[34m##########[0m| 1/1 [00:00<00:00, 14.14it/s]
[2025-06-10 14:40:59][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:40:59][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.006: 100%|[34m##########[0m| 1/1 [00:00<00:00, 13.71it/s]
[2025-06-10 14:40:59][ThreadPoolExecutor-0_0][ERROR]: 100%|[31m##########[0m| 1/1 [00:00<00:00,  5.77it/s]
[2025-06-10 14:40:59][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.006, time_speech:  13.704, time_escape: 0.080: 100%|[31m##########[0m| 1/1 [00:00<00:00,  5.77it/s]
[2025-06-10 14:40:59][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.006, time_speech:  13.704, time_escape: 0.080: 100%|[31m##########[0m| 1/1 [00:00<00:00,  5.75it/s]
[2025-06-10 14:40:59][ThreadPoolExecutor-0_0][INFO]: 处理文本: 南晚会时项目标计算法数据标注钢精测计算法测试数据标注。
[2025-06-10 14:40:59][ThreadPoolExecutor-0_0][INFO]: funasr推理原始结果: <|zh|><|NEUTRAL|><|Speech|><|withitn|>南晚会时项目标计算法数据标注钢精测计算法测试数据标注。
[2025-06-10 14:40:59][ThreadPoolExecutor-0_0][INFO]: funasr处理后结果: 南晚会时项目标计算法数据标注钢精测计算法测试数据标注。
[2025-06-10 14:40:59][ThreadPoolExecutor-0_0][INFO]: funasr语音识别完成 - 总耗时: 0.326秒 | 验证: 0.000秒 | 文件准备: 0.001秒 | 模型推理: 0.324秒 | 清理: 0.000秒
[2025-06-10 14:40:59][ThreadPoolExecutor-0_0][INFO]: 任务 74dbc66f-e93e-4c11-a75c-08a17f1caaaa 完成，处理时间: 0.33秒，结果数量: 1 

[2025-06-10 14:40:59][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: 78b00fa7-418a-4ead-b72e-5e7e55907a69
[2025-06-10 14:40:59][ThreadPoolExecutor-0_0][INFO]: 任务 78b00fa7-418a-4ead-b72e-5e7e55907a69: 音频数量=1, 语言=auto
[2025-06-10 14:40:59][ThreadPoolExecutor-0_0][INFO]: 开始funasr语音识别推理，音频数量: 1, 语言: auto
[2025-06-10 14:40:59][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:40:59][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.001', 'extract_feat': '0.008', 'forward': '0.032', 'batch_size': '1', 'rtf': '0.006'}, : 100%|[34m##########[0m| 1/1 [00:00<00:00, 30.86it/s]
[2025-06-10 14:40:59][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.006: 100%|[34m##########[0m| 1/1 [00:00<00:00, 30.37it/s]
[2025-06-10 14:40:59][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.006: 100%|[34m##########[0m| 1/1 [00:00<00:00, 29.60it/s]
[2025-06-10 14:40:59][ThreadPoolExecutor-0_0][ERROR]: 0%|[31m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:40:59][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:40:59][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:41:00][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.000', 'extract_feat': '0.006', 'forward': '0.056', 'batch_size': '1', 'rtf': '0.011'}, : 100%|[34m##########[0m| 1/1 [00:00<00:00, 17.92it/s]
[2025-06-10 14:41:00][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:41:00][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.011: 100%|[34m##########[0m| 1/1 [00:00<00:00, 17.74it/s]
[2025-06-10 14:41:00][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:41:00][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.011: 100%|[34m##########[0m| 1/1 [00:00<00:00, 17.34it/s]
[2025-06-10 14:41:00][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.012, time_speech:  5.547, time_escape: 0.064: 100%|[31m##########[0m| 1/1 [00:00<00:00, 15.29it/s]
[2025-06-10 14:41:00][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.012, time_speech:  5.547, time_escape: 0.064: 100%|[31m##########[0m| 1/1 [00:00<00:00, 15.24it/s]
[2025-06-10 14:41:00][ThreadPoolExecutor-0_0][INFO]: 处理文本: 欢迎大家来体验达摩院推出的语音识别模型。
[2025-06-10 14:41:00][ThreadPoolExecutor-0_0][INFO]: funasr推理原始结果: <|zh|><|NEUTRAL|><|Speech|><|withitn|>欢迎大家来体验达摩院推出的语音识别模型。
[2025-06-10 14:41:00][ThreadPoolExecutor-0_0][INFO]: funasr处理后结果: 欢迎大家来体验达摩院推出的语音识别模型。
[2025-06-10 14:41:00][ThreadPoolExecutor-0_0][INFO]: funasr语音识别完成 - 总耗时: 0.102秒 | 验证: 0.000秒 | 文件准备: 0.000秒 | 模型推理: 0.101秒 | 清理: 0.000秒
[2025-06-10 14:41:00][ThreadPoolExecutor-0_0][INFO]: 任务 78b00fa7-418a-4ead-b72e-5e7e55907a69 完成，处理时间: 0.10秒，结果数量: 1 

[2025-06-10 14:41:00][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: 0c85ff55-9c7a-414e-9771-c37f3762c536
[2025-06-10 14:41:00][ThreadPoolExecutor-0_0][INFO]: 任务 0c85ff55-9c7a-414e-9771-c37f3762c536: 音频数量=1, 语言=auto
[2025-06-10 14:41:00][ThreadPoolExecutor-0_0][INFO]: 开始funasr语音识别推理，音频数量: 1, 语言: auto
[2025-06-10 14:41:00][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:41:00][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.001', 'extract_feat': '0.005', 'forward': '0.027', 'batch_size': '1', 'rtf': '0.003'}, : 100%|[34m##########[0m| 1/1 [00:00<00:00, 37.49it/s]
[2025-06-10 14:41:00][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.003: 100%|[34m##########[0m| 1/1 [00:00<00:00, 36.93it/s]
[2025-06-10 14:41:00][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.003: 100%|[34m##########[0m| 1/1 [00:00<00:00, 35.86it/s]
[2025-06-10 14:41:00][ThreadPoolExecutor-0_0][ERROR]: 0%|[31m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:41:00][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:41:00][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:41:00][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.000', 'extract_feat': '0.008', 'forward': '0.076', 'batch_size': '1', 'rtf': '0.010'}, : 100%|[34m##########[0m| 1/1 [00:00<00:00, 13.22it/s]
[2025-06-10 14:41:00][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:41:00][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.010: 100%|[34m##########[0m| 1/1 [00:00<00:00, 13.08it/s]
[2025-06-10 14:41:00][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:41:00][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.010: 100%|[34m##########[0m| 1/1 [00:00<00:00, 12.76it/s]
[2025-06-10 14:41:00][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.010, time_speech:  7.925, time_escape: 0.083: 100%|[31m##########[0m| 1/1 [00:00<00:00, 11.46it/s]
[2025-06-10 14:41:00][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.010, time_speech:  7.925, time_escape: 0.083: 100%|[31m##########[0m| 1/1 [00:00<00:00, 11.42it/s]
[2025-06-10 14:41:00][ThreadPoolExecutor-0_0][INFO]: 处理文本: 国务院发展研究中心市场经济研究所副所长邓玉松认为。
[2025-06-10 14:41:00][ThreadPoolExecutor-0_0][INFO]: funasr推理原始结果: <|zh|><|NEUTRAL|><|Speech|><|withitn|>国务院发展研究中心市场经济研究所副所长邓玉松认为。
[2025-06-10 14:41:00][ThreadPoolExecutor-0_0][INFO]: funasr处理后结果: 国务院发展研究中心市场经济研究所副所长邓玉松认为。
[2025-06-10 14:41:00][ThreadPoolExecutor-0_0][INFO]: funasr语音识别完成 - 总耗时: 0.118秒 | 验证: 0.000秒 | 文件准备: 0.000秒 | 模型推理: 0.117秒 | 清理: 0.000秒
[2025-06-10 14:41:00][ThreadPoolExecutor-0_0][INFO]: 任务 0c85ff55-9c7a-414e-9771-c37f3762c536 完成，处理时间: 0.12秒，结果数量: 1 

[2025-06-10 14:41:00][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: 61444ac2-965c-4867-9556-ee59e453f8ac
[2025-06-10 14:41:00][ThreadPoolExecutor-0_0][INFO]: 任务 61444ac2-965c-4867-9556-ee59e453f8ac: 音频数量=1, 语言=auto
[2025-06-10 14:41:00][ThreadPoolExecutor-0_0][INFO]: 开始funasr语音识别推理，音频数量: 1, 语言: auto
[2025-06-10 14:41:00][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:41:00][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.001', 'extract_feat': '0.004', 'forward': '0.022', 'batch_size': '1', 'rtf': '0.004'}, : 100%|[34m##########[0m| 1/1 [00:00<00:00, 44.55it/s]
[2025-06-10 14:41:00][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004: 100%|[34m##########[0m| 1/1 [00:00<00:00, 43.71it/s]
[2025-06-10 14:41:00][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004: 100%|[34m##########[0m| 1/1 [00:00<00:00, 42.29it/s]
[2025-06-10 14:41:00][ThreadPoolExecutor-0_0][ERROR]: 0%|[31m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:41:00][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:41:00][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:41:00][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.000', 'extract_feat': '0.005', 'forward': '0.073', 'batch_size': '1', 'rtf': '0.013'}, : 100%|[34m##########[0m| 1/1 [00:00<00:00, 13.68it/s]
[2025-06-10 14:41:00][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:41:00][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.013: 100%|[34m##########[0m| 1/1 [00:00<00:00, 13.49it/s]
[2025-06-10 14:41:00][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:41:00][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.013: 100%|[34m##########[0m| 1/1 [00:00<00:00, 13.20it/s]
[2025-06-10 14:41:00][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.014, time_speech:  5.848, time_escape: 0.081: 100%|[31m##########[0m| 1/1 [00:00<00:00, 12.17it/s]
[2025-06-10 14:41:00][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.014, time_speech:  5.848, time_escape: 0.081: 100%|[31m##########[0m| 1/1 [00:00<00:00, 12.12it/s]
[2025-06-10 14:41:00][ThreadPoolExecutor-0_0][INFO]: 处理文本: 新洲一名高中新生，因为没去教室上晚自习。
[2025-06-10 14:41:00][ThreadPoolExecutor-0_0][INFO]: funasr推理原始结果: <|zh|><|NEUTRAL|><|Speech|><|withitn|>新洲一名高中新生，因为没去教室上晚自习。
[2025-06-10 14:41:00][ThreadPoolExecutor-0_0][INFO]: funasr处理后结果: 新洲一名高中新生，因为没去教室上晚自习。
[2025-06-10 14:41:00][ThreadPoolExecutor-0_0][INFO]: funasr语音识别完成 - 总耗时: 0.110秒 | 验证: 0.000秒 | 文件准备: 0.001秒 | 模型推理: 0.109秒 | 清理: 0.000秒
[2025-06-10 14:41:00][ThreadPoolExecutor-0_0][INFO]: 任务 61444ac2-965c-4867-9556-ee59e453f8ac 完成，处理时间: 0.11秒，结果数量: 1 

[2025-06-10 14:41:00][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: 9c69afa1-756c-41e8-a243-308a5880a826
[2025-06-10 14:41:00][ThreadPoolExecutor-0_0][INFO]: 任务 9c69afa1-756c-41e8-a243-308a5880a826: 音频数量=1, 语言=auto
[2025-06-10 14:41:00][ThreadPoolExecutor-0_0][INFO]: 开始funasr语音识别推理，音频数量: 1, 语言: auto
[2025-06-10 14:41:00][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:41:00][ThreadPoolExecutor-0_0][ERROR]: 100%|[34m##########[0m| 1/1 [00:00<00:00,  3.52it/s]
[2025-06-10 14:41:00][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.010', 'extract_feat': '0.241', 'forward': '0.284', 'batch_size': '1', 'rtf': '0.027'}, : 100%|[34m##########[0m| 1/1 [00:00<00:00,  3.52it/s]
[2025-06-10 14:41:00][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.027: 100%|[34m##########[0m| 1/1 [00:00<00:00,  3.52it/s]
[2025-06-10 14:41:00][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.027: 100%|[34m##########[0m| 1/1 [00:00<00:00,  3.47it/s]
[2025-06-10 14:41:00][ThreadPoolExecutor-0_0][ERROR]: 0%|[31m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:41:00][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/5 [00:00<?, ?it/s]
[2025-06-10 14:41:00][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:41:00][ThreadPoolExecutor-0_0][ERROR]: 100%|[34m##########[0m| 5/5 [00:00<00:00, 22.55it/s]
[2025-06-10 14:41:00][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:41:00][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.000', 'extract_feat': '0.057', 'forward': '0.222', 'batch_size': '5', 'rtf': '0.004'}, : 100%|[34m##########[0m| 5/5 [00:00<00:00, 22.55it/s]
[2025-06-10 14:41:00][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:41:00][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004: 100%|[34m##########[0m| 5/5 [00:00<00:00, 22.55it/s]
[2025-06-10 14:41:00][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:41:00][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004: 100%|[34m##########[0m| 5/5 [00:00<00:00, 21.59it/s]
[2025-06-10 14:41:00][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:41:00][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:41:00][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.000', 'extract_feat': '0.021', 'forward': '0.073', 'batch_size': '1', 'rtf': '0.004'}, : 100%|[34m##########[0m| 1/1 [00:00<00:00, 13.61it/s]
[2025-06-10 14:41:00][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:41:00][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004: 100%|[34m##########[0m| 1/1 [00:00<00:00, 13.50it/s]
[2025-06-10 14:41:00][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:41:00][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004: 100%|[34m##########[0m| 1/1 [00:00<00:00, 12.95it/s]
[2025-06-10 14:41:00][ThreadPoolExecutor-0_0][ERROR]: 100%|[31m##########[0m| 1/1 [00:00<00:00,  3.04it/s]
[2025-06-10 14:41:00][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.005, time_speech:  70.471, time_escape: 0.320: 100%|[31m##########[0m| 1/1 [00:00<00:00,  3.04it/s]
[2025-06-10 14:41:00][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.005, time_speech:  70.471, time_escape: 0.320: 100%|[31m##########[0m| 1/1 [00:00<00:00,  3.04it/s]
[2025-06-10 14:41:00][ThreadPoolExecutor-0_0][INFO]: 处理文本: 试错的过程很简单而，且特别是今天报名仓雪卡的同学，你们可以。听到后面的有专门的活动课，他会大大降低你的试绸成本。其实你也可以过来听课，为什么你自己写嘛？我先今天写5个点，我就试试试验一下，反正这5个点不行，我再写5个点，这试再不行，那再写5个点嘛。你总会所谓的活动搭神和所谓的高手，都是只有一个把所有的错，所有的坑全部趟一遍，留下正确的你就是所谓的搭神。明白吗？所以说关于活动通过这块，我只送给你们四个字啊，换位思考。如果说你要想降低你的试错成本，今天来这里你们就是对的。因为有畅畅血卡这个机会，所以说关于活动过于不过这个问题，或者活动很难通过这个话题呃，如果真的要坐下来聊的话，要聊一天。😡但是我觉得我刚才说的四个字足够。好，谢谢。好，非常感谢那个三茂老师的回答啊。三茂老师说，我们在整个店铺的这个活动当中，我们要学会换位思考。其实。😊
[2025-06-10 14:41:00][ThreadPoolExecutor-0_0][INFO]: funasr推理原始结果: <|zh|><|ANGRY|><|Speech|><|withitn|>试错的过程很简单而，且特别是今天报名仓雪卡的同学，你们可以。 <|zh|><|ANGRY|><|Speech|><|withitn|>听到后面的有专门的活动课，他会大大降低你的试绸成本。其实你也可以过来听课，为什么你自己写嘛？我先今天写5个点，我就试试试验一下，反正这5个点不行，我再写5个点，这试再不行，那再写5个点嘛。 <|zh|><|ANGRY|><|Speech|><|withitn|>你总会所谓的活动搭神和所谓的高手，都是只有一个把所有的错，所有的坑全部趟一遍，留下正确的你就是所谓的搭神。 <|zh|><|ANGRY|><|Speech|><|withitn|>明白吗？所以说关于活动通过这块，我只送给你们四个字啊，换位思考。如果说你要想降低你的试错成本，今天来这里你们就是对的。 <|zh|><|ANGRY|><|Speech|><|withitn|>因为有畅畅血卡这个机会，所以说关于活动过于不过这个问题，或者活动很难通过这个话题呃，如果真的要坐下来聊的话，要聊一天。 <|zh|><|HAPPY|><|Speech|><|withitn|>但是我觉得我刚才说的四个字足够。好，谢谢。好，非常感谢那个三茂老师的回答啊。三茂老师说，我们在整个店铺的这个活动当中，我们要学会换位思考。其实。
[2025-06-10 14:41:00][ThreadPoolExecutor-0_0][INFO]: funasr处理后结果: 试错的过程很简单而，且特别是今天报名仓雪卡的同学，你们可以。听到后面的有专门的活动课，他会大大降低你的试绸成本。其实你也可以过来听课，为什么你自己写嘛？我先今天写5个点，我就试试试验一下，反正这5个点不行，我再写5个点，这试再不行，那再写5个点嘛。你总会所谓的活动搭神和所谓的高手，都是只有一个把所有的错，所有的坑全部趟一遍，留下正确的你就是所谓的搭神。明白吗？所以说关于活动通过这块，我只送给你们四个字啊，换位思考。如果说你要想降低你的试错成本，今天来这里你们就是对的。因为有畅畅血卡这个机会，所以说关于活动过于不过这个问题，或者活动很难通过这个话题呃，如果真的要坐下来聊的话，要聊一天。😡但是我觉得我刚才说的四个字足够。好，谢谢。好，非常感谢那个三茂老师的回答啊。三茂老师说，我们在整个店铺的这个活动当中，我们要学会换位思考。其实。😊
[2025-06-10 14:41:00][ThreadPoolExecutor-0_0][INFO]: funasr语音识别完成 - 总耗时: 0.623秒 | 验证: 0.000秒 | 文件准备: 0.003秒 | 模型推理: 0.620秒 | 清理: 0.000秒
[2025-06-10 14:41:00][ThreadPoolExecutor-0_0][INFO]: 任务 9c69afa1-756c-41e8-a243-308a5880a826 完成，处理时间: 0.63秒，结果数量: 1 

[2025-06-10 14:41:00][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: 1c8e4ba0-1f7e-4e4b-a9b8-f51a7e62f36a
[2025-06-10 14:41:00][ThreadPoolExecutor-0_0][INFO]: 任务 1c8e4ba0-1f7e-4e4b-a9b8-f51a7e62f36a: 音频数量=5, 语言=auto
[2025-06-10 14:41:00][ThreadPoolExecutor-0_0][INFO]: 开始funasr语音识别推理，音频数量: 5, 语言: auto
[2025-06-10 14:41:00][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:41:01][ThreadPoolExecutor-0_0][ERROR]: 100%|[34m##########[0m| 1/1 [00:00<00:00,  8.48it/s]
[2025-06-10 14:41:01][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.063', 'extract_feat': '0.008', 'forward': '0.118', 'batch_size': '1', 'rtf': '0.009'}, : 100%|[34m##########[0m| 1/1 [00:00<00:00,  8.48it/s]
[2025-06-10 14:41:01][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.009: 100%|[34m##########[0m| 1/1 [00:00<00:00,  8.48it/s]
[2025-06-10 14:41:01][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.009: 100%|[34m##########[0m| 1/1 [00:00<00:00,  8.33it/s]
[2025-06-10 14:41:01][ThreadPoolExecutor-0_0][ERROR]: 0%|[31m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:41:01][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:41:01][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:41:01][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.000', 'extract_feat': '0.008', 'forward': '0.074', 'batch_size': '1', 'rtf': '0.006'}, : 100%|[34m##########[0m| 1/1 [00:00<00:00, 13.50it/s]
[2025-06-10 14:41:01][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:41:01][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.006: 100%|[34m##########[0m| 1/1 [00:00<00:00, 13.37it/s]
[2025-06-10 14:41:01][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:41:01][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.006: 100%|[34m##########[0m| 1/1 [00:00<00:00, 12.97it/s]
[2025-06-10 14:41:01][ThreadPoolExecutor-0_0][ERROR]: 100%|[31m##########[0m| 1/1 [00:00<00:00,  6.05it/s]
[2025-06-10 14:41:01][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.006, time_speech:  13.704, time_escape: 0.081: 100%|[31m##########[0m| 1/1 [00:00<00:00,  6.05it/s]
[2025-06-10 14:41:01][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.006, time_speech:  13.704, time_escape: 0.081: 100%|[31m##########[0m| 1/1 [00:00<00:00,  6.03it/s]
[2025-06-10 14:41:01][ThreadPoolExecutor-0_0][INFO]: 处理文本: 南晚会时项目标计算法数据标注钢精测计算法测试数据标注。
[2025-06-10 14:41:01][ThreadPoolExecutor-0_0][INFO]: funasr推理原始结果: <|zh|><|NEUTRAL|><|Speech|><|withitn|>南晚会时项目标计算法数据标注钢精测计算法测试数据标注。
[2025-06-10 14:41:01][ThreadPoolExecutor-0_0][INFO]: funasr处理后结果: 南晚会时项目标计算法数据标注钢精测计算法测试数据标注。
[2025-06-10 14:41:01][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:41:01][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.001', 'extract_feat': '0.004', 'forward': '0.030', 'batch_size': '1', 'rtf': '0.005'}, : 100%|[34m##########[0m| 1/1 [00:00<00:00, 33.69it/s]
[2025-06-10 14:41:01][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.005: 100%|[34m##########[0m| 1/1 [00:00<00:00, 33.13it/s]
[2025-06-10 14:41:01][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.005: 100%|[34m##########[0m| 1/1 [00:00<00:00, 32.27it/s]
[2025-06-10 14:41:01][ThreadPoolExecutor-0_0][ERROR]: 0%|[31m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:41:01][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:41:01][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:41:01][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.000', 'extract_feat': '0.008', 'forward': '0.065', 'batch_size': '1', 'rtf': '0.013'}, : 100%|[34m##########[0m| 1/1 [00:00<00:00, 15.25it/s]
[2025-06-10 14:41:01][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:41:01][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.013: 100%|[34m##########[0m| 1/1 [00:00<00:00, 15.09it/s]
[2025-06-10 14:41:01][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:41:01][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.013: 100%|[34m##########[0m| 1/1 [00:00<00:00, 14.78it/s]
[2025-06-10 14:41:01][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.013, time_speech:  5.547, time_escape: 0.074: 100%|[31m##########[0m| 1/1 [00:00<00:00, 13.26it/s]
[2025-06-10 14:41:01][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.013, time_speech:  5.547, time_escape: 0.074: 100%|[31m##########[0m| 1/1 [00:00<00:00, 13.20it/s]
[2025-06-10 14:41:01][ThreadPoolExecutor-0_0][INFO]: 处理文本: 欢迎大家来体验达摩院推出的语音识别模型。
[2025-06-10 14:41:01][ThreadPoolExecutor-0_0][INFO]: funasr推理原始结果: <|zh|><|NEUTRAL|><|Speech|><|withitn|>欢迎大家来体验达摩院推出的语音识别模型。
[2025-06-10 14:41:01][ThreadPoolExecutor-0_0][INFO]: funasr处理后结果: 欢迎大家来体验达摩院推出的语音识别模型。
[2025-06-10 14:41:01][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:41:01][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.003', 'extract_feat': '0.005', 'forward': '0.034', 'batch_size': '1', 'rtf': '0.004'}, : 100%|[34m##########[0m| 1/1 [00:00<00:00, 29.28it/s]
[2025-06-10 14:41:01][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004: 100%|[34m##########[0m| 1/1 [00:00<00:00, 28.95it/s]
[2025-06-10 14:41:01][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004: 100%|[34m##########[0m| 1/1 [00:00<00:00, 28.25it/s]
[2025-06-10 14:41:01][ThreadPoolExecutor-0_0][ERROR]: 0%|[31m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:41:01][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:41:01][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:41:01][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.000', 'extract_feat': '0.006', 'forward': '0.063', 'batch_size': '1', 'rtf': '0.008'}, : 100%|[34m##########[0m| 1/1 [00:00<00:00, 15.96it/s]
[2025-06-10 14:41:01][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:41:01][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.008: 100%|[34m##########[0m| 1/1 [00:00<00:00, 15.78it/s]
[2025-06-10 14:41:01][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:41:01][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.008: 100%|[34m##########[0m| 1/1 [00:00<00:00, 15.32it/s]
[2025-06-10 14:41:01][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.009, time_speech:  7.925, time_escape: 0.069: 100%|[31m##########[0m| 1/1 [00:00<00:00, 14.05it/s]
[2025-06-10 14:41:01][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.009, time_speech:  7.925, time_escape: 0.069: 100%|[31m##########[0m| 1/1 [00:00<00:00, 14.00it/s]
[2025-06-10 14:41:01][ThreadPoolExecutor-0_0][INFO]: 处理文本: 国务院发展研究中心市场经济研究所副所长邓玉松认为。
[2025-06-10 14:41:01][ThreadPoolExecutor-0_0][INFO]: funasr推理原始结果: <|zh|><|NEUTRAL|><|Speech|><|withitn|>国务院发展研究中心市场经济研究所副所长邓玉松认为。
[2025-06-10 14:41:01][ThreadPoolExecutor-0_0][INFO]: funasr处理后结果: 国务院发展研究中心市场经济研究所副所长邓玉松认为。
[2025-06-10 14:41:01][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:41:01][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.001', 'extract_feat': '0.004', 'forward': '0.030', 'batch_size': '1', 'rtf': '0.005'}, : 100%|[34m##########[0m| 1/1 [00:00<00:00, 33.29it/s]
[2025-06-10 14:41:01][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.005: 100%|[34m##########[0m| 1/1 [00:00<00:00, 32.76it/s]
[2025-06-10 14:41:01][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.005: 100%|[34m##########[0m| 1/1 [00:00<00:00, 31.91it/s]
[2025-06-10 14:41:01][ThreadPoolExecutor-0_0][ERROR]: 0%|[31m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:41:01][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:41:01][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:41:01][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.000', 'extract_feat': '0.006', 'forward': '0.061', 'batch_size': '1', 'rtf': '0.011'}, : 100%|[34m##########[0m| 1/1 [00:00<00:00, 16.25it/s]
[2025-06-10 14:41:01][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:41:01][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.011: 100%|[34m##########[0m| 1/1 [00:00<00:00, 16.10it/s]
[2025-06-10 14:41:01][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:41:01][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.011: 100%|[34m##########[0m| 1/1 [00:00<00:00, 15.79it/s]
[2025-06-10 14:41:01][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.012, time_speech:  5.848, time_escape: 0.070: 100%|[31m##########[0m| 1/1 [00:00<00:00, 14.15it/s]
[2025-06-10 14:41:01][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.012, time_speech:  5.848, time_escape: 0.070: 100%|[31m##########[0m| 1/1 [00:00<00:00, 14.10it/s]
[2025-06-10 14:41:01][ThreadPoolExecutor-0_0][INFO]: 处理文本: 新洲一名高中新生，因为没去教室上晚自习。
[2025-06-10 14:41:01][ThreadPoolExecutor-0_0][INFO]: funasr推理原始结果: <|zh|><|NEUTRAL|><|Speech|><|withitn|>新洲一名高中新生，因为没去教室上晚自习。
[2025-06-10 14:41:01][ThreadPoolExecutor-0_0][INFO]: funasr处理后结果: 新洲一名高中新生，因为没去教室上晚自习。
[2025-06-10 14:41:01][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:41:01][ThreadPoolExecutor-0_0][ERROR]: 100%|[34m##########[0m| 1/1 [00:00<00:00,  3.46it/s]
[2025-06-10 14:41:01][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.008', 'extract_feat': '0.237', 'forward': '0.289', 'batch_size': '1', 'rtf': '0.028'}, : 100%|[34m##########[0m| 1/1 [00:00<00:00,  3.46it/s]
[2025-06-10 14:41:01][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.028: 100%|[34m##########[0m| 1/1 [00:00<00:00,  3.46it/s]
[2025-06-10 14:41:01][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.028: 100%|[34m##########[0m| 1/1 [00:00<00:00,  3.40it/s]
[2025-06-10 14:41:01][ThreadPoolExecutor-0_0][ERROR]: 0%|[31m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:41:01][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/5 [00:00<?, ?it/s]
[2025-06-10 14:41:01][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:41:02][ThreadPoolExecutor-0_0][ERROR]: 100%|[34m##########[0m| 5/5 [00:00<00:00, 27.40it/s]
[2025-06-10 14:41:02][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:41:02][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.000', 'extract_feat': '0.041', 'forward': '0.182', 'batch_size': '5', 'rtf': '0.003'}, : 100%|[34m##########[0m| 5/5 [00:00<00:00, 27.40it/s]
[2025-06-10 14:41:02][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:41:02][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.003: 100%|[34m##########[0m| 5/5 [00:00<00:00, 27.40it/s]
[2025-06-10 14:41:02][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:41:02][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.003: 100%|[34m##########[0m| 5/5 [00:00<00:00, 25.81it/s]
[2025-06-10 14:41:02][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:41:02][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:41:02][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.000', 'extract_feat': '0.018', 'forward': '0.070', 'batch_size': '1', 'rtf': '0.004'}, : 100%|[34m##########[0m| 1/1 [00:00<00:00, 14.20it/s]
[2025-06-10 14:41:02][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:41:02][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004: 100%|[34m##########[0m| 1/1 [00:00<00:00, 14.06it/s]
[2025-06-10 14:41:02][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:41:02][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004: 100%|[34m##########[0m| 1/1 [00:00<00:00, 13.42it/s]
[2025-06-10 14:41:02][ThreadPoolExecutor-0_0][ERROR]: 100%|[31m##########[0m| 1/1 [00:00<00:00,  3.47it/s]
[2025-06-10 14:41:02][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004, time_speech:  70.471, time_escape: 0.280: 100%|[31m##########[0m| 1/1 [00:00<00:00,  3.47it/s]
[2025-06-10 14:41:02][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004, time_speech:  70.471, time_escape: 0.280: 100%|[31m##########[0m| 1/1 [00:00<00:00,  3.46it/s]
[2025-06-10 14:41:02][ThreadPoolExecutor-0_0][INFO]: 处理文本: 试错的过程很简单而，且特别是今天报名仓雪卡的同学，你们可以。听到后面的有专门的活动课，他会大大降低你的试绸成本。其实你也可以过来听课，为什么你自己写嘛？我先今天写5个点，我就试试试验一下，反正这5个点不行，我再写5个点，这试再不行，那再写5个点嘛。你总会所谓的活动搭神和所谓的高手，都是只有一个把所有的错，所有的坑全部趟一遍，留下正确的你就是所谓的搭神。明白吗？所以说关于活动通过这块，我只送给你们四个字啊，换位思考。如果说你要想降低你的试错成本，今天来这里你们就是对的。因为有畅血畅血卡这个机会，所以说关于活动过于不过这个问题，或者活动很难通过这个话题呃，如果真的要坐下来聊的话，要聊一天。😡但是我觉得我刚才说的四个字足够。好，谢谢。好，非常感谢那个三茂老师的回答啊。三茂老师说，我们在整个店铺的这个活动当中，我们要学会换位思考。其实。😊
[2025-06-10 14:41:02][ThreadPoolExecutor-0_0][INFO]: funasr推理原始结果: <|zh|><|ANGRY|><|Speech|><|withitn|>试错的过程很简单而，且特别是今天报名仓雪卡的同学，你们可以。 <|zh|><|ANGRY|><|Speech|><|withitn|>听到后面的有专门的活动课，他会大大降低你的试绸成本。其实你也可以过来听课，为什么你自己写嘛？我先今天写5个点，我就试试试验一下，反正这5个点不行，我再写5个点，这试再不行，那再写5个点嘛。 <|zh|><|ANGRY|><|Speech|><|withitn|>你总会所谓的活动搭神和所谓的高手，都是只有一个把所有的错，所有的坑全部趟一遍，留下正确的你就是所谓的搭神。 <|zh|><|ANGRY|><|Speech|><|withitn|>明白吗？所以说关于活动通过这块，我只送给你们四个字啊，换位思考。如果说你要想降低你的试错成本，今天来这里你们就是对的。 <|zh|><|ANGRY|><|Speech|><|withitn|>因为有畅血畅血卡这个机会，所以说关于活动过于不过这个问题，或者活动很难通过这个话题呃，如果真的要坐下来聊的话，要聊一天。 <|zh|><|HAPPY|><|Speech|><|withitn|>但是我觉得我刚才说的四个字足够。好，谢谢。好，非常感谢那个三茂老师的回答啊。三茂老师说，我们在整个店铺的这个活动当中，我们要学会换位思考。其实。
[2025-06-10 14:41:02][ThreadPoolExecutor-0_0][INFO]: funasr处理后结果: 试错的过程很简单而，且特别是今天报名仓雪卡的同学，你们可以。听到后面的有专门的活动课，他会大大降低你的试绸成本。其实你也可以过来听课，为什么你自己写嘛？我先今天写5个点，我就试试试验一下，反正这5个点不行，我再写5个点，这试再不行，那再写5个点嘛。你总会所谓的活动搭神和所谓的高手，都是只有一个把所有的错，所有的坑全部趟一遍，留下正确的你就是所谓的搭神。明白吗？所以说关于活动通过这块，我只送给你们四个字啊，换位思考。如果说你要想降低你的试错成本，今天来这里你们就是对的。因为有畅血畅血卡这个机会，所以说关于活动过于不过这个问题，或者活动很难通过这个话题呃，如果真的要坐下来聊的话，要聊一天。😡但是我觉得我刚才说的四个字足够。好，谢谢。好，非常感谢那个三茂老师的回答啊。三茂老师说，我们在整个店铺的这个活动当中，我们要学会换位思考。其实。😊
[2025-06-10 14:41:02][ThreadPoolExecutor-0_0][INFO]: funasr语音识别完成 - 总耗时: 1.200秒 | 验证: 0.000秒 | 文件准备: 0.003秒 | 模型推理: 1.195秒 | 清理: 0.001秒
[2025-06-10 14:41:02][ThreadPoolExecutor-0_0][INFO]: 任务 1c8e4ba0-1f7e-4e4b-a9b8-f51a7e62f36a 完成，处理时间: 1.20秒，结果数量: 5 

[2025-06-10 14:41:24][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: d83a072b-1e08-4f89-8898-8e9033445b27
[2025-06-10 14:41:24][ThreadPoolExecutor-0_0][INFO]: 任务 d83a072b-1e08-4f89-8898-8e9033445b27: 音频数量=1, 语言=auto
[2025-06-10 14:41:24][ThreadPoolExecutor-0_0][INFO]: 开始funasr语音识别推理，音频数量: 1, 语言: auto
[2025-06-10 14:41:24][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:41:24][ThreadPoolExecutor-0_0][ERROR]: 100%|[34m##########[0m| 1/1 [00:00<00:00,  5.38it/s]
[2025-06-10 14:41:24][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.135', 'extract_feat': '0.007', 'forward': '0.186', 'batch_size': '1', 'rtf': '0.014'}, : 100%|[34m##########[0m| 1/1 [00:00<00:00,  5.38it/s]
[2025-06-10 14:41:24][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.014: 100%|[34m##########[0m| 1/1 [00:00<00:00,  5.38it/s]
[2025-06-10 14:41:24][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.014: 100%|[34m##########[0m| 1/1 [00:00<00:00,  5.31it/s]
[2025-06-10 14:41:24][ThreadPoolExecutor-0_0][ERROR]: 0%|[31m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:41:24][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:41:24][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:41:24][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.000', 'extract_feat': '0.010', 'forward': '0.074', 'batch_size': '1', 'rtf': '0.006'}, : 100%|[34m##########[0m| 1/1 [00:00<00:00, 13.45it/s]
[2025-06-10 14:41:24][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:41:24][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.006: 100%|[34m##########[0m| 1/1 [00:00<00:00, 13.30it/s]
[2025-06-10 14:41:24][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:41:24][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.006: 100%|[34m##########[0m| 1/1 [00:00<00:00, 12.54it/s]
[2025-06-10 14:41:24][ThreadPoolExecutor-0_0][ERROR]: 100%|[31m##########[0m| 1/1 [00:00<00:00,  6.54it/s]
[2025-06-10 14:41:24][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.006, time_speech:  13.704, time_escape: 0.084: 100%|[31m##########[0m| 1/1 [00:00<00:00,  6.54it/s]
[2025-06-10 14:41:24][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.006, time_speech:  13.704, time_escape: 0.084: 100%|[31m##########[0m| 1/1 [00:00<00:00,  6.52it/s]
[2025-06-10 14:41:24][ThreadPoolExecutor-0_0][INFO]: 处理文本: 南晚会时项目标计算法数据标注钢精测计算法测试数据标注。
[2025-06-10 14:41:24][ThreadPoolExecutor-0_0][INFO]: funasr推理原始结果: <|zh|><|NEUTRAL|><|Speech|><|withitn|>南晚会时项目标计算法数据标注钢精测计算法测试数据标注。
[2025-06-10 14:41:24][ThreadPoolExecutor-0_0][INFO]: funasr处理后结果: 南晚会时项目标计算法数据标注钢精测计算法测试数据标注。
[2025-06-10 14:41:24][ThreadPoolExecutor-0_0][INFO]: funasr语音识别完成 - 总耗时: 0.348秒 | 验证: 0.000秒 | 文件准备: 0.001秒 | 模型推理: 0.345秒 | 清理: 0.000秒
[2025-06-10 14:41:24][ThreadPoolExecutor-0_0][INFO]: 任务 d83a072b-1e08-4f89-8898-8e9033445b27 完成，处理时间: 0.35秒，结果数量: 1 

[2025-06-10 14:41:24][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: f64f1410-ec68-422c-a373-4278eb959fd9
[2025-06-10 14:41:24][ThreadPoolExecutor-0_0][INFO]: 任务 f64f1410-ec68-422c-a373-4278eb959fd9: 音频数量=1, 语言=auto
[2025-06-10 14:41:24][ThreadPoolExecutor-0_0][INFO]: 开始funasr语音识别推理，音频数量: 1, 语言: auto
[2025-06-10 14:41:24][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:41:24][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.001', 'extract_feat': '0.003', 'forward': '0.027', 'batch_size': '1', 'rtf': '0.005'}, : 100%|[34m##########[0m| 1/1 [00:00<00:00, 36.79it/s]
[2025-06-10 14:41:24][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.005: 100%|[34m##########[0m| 1/1 [00:00<00:00, 36.14it/s]
[2025-06-10 14:41:24][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.005: 100%|[34m##########[0m| 1/1 [00:00<00:00, 35.03it/s]
[2025-06-10 14:41:24][ThreadPoolExecutor-0_0][ERROR]: 0%|[31m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:41:24][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:41:24][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:41:24][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.000', 'extract_feat': '0.004', 'forward': '0.056', 'batch_size': '1', 'rtf': '0.011'}, : 100%|[34m##########[0m| 1/1 [00:00<00:00, 17.98it/s]
[2025-06-10 14:41:24][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:41:24][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.011: 100%|[34m##########[0m| 1/1 [00:00<00:00, 17.70it/s]
[2025-06-10 14:41:24][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:41:24][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.011: 100%|[34m##########[0m| 1/1 [00:00<00:00, 16.87it/s]
[2025-06-10 14:41:24][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.011, time_speech:  5.547, time_escape: 0.064: 100%|[31m##########[0m| 1/1 [00:00<00:00, 15.48it/s]
[2025-06-10 14:41:24][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.011, time_speech:  5.547, time_escape: 0.064: 100%|[31m##########[0m| 1/1 [00:00<00:00, 15.41it/s]
[2025-06-10 14:41:24][ThreadPoolExecutor-0_0][INFO]: 处理文本: 欢迎大家来体验达摩院推出的语音识别模型。
[2025-06-10 14:41:24][ThreadPoolExecutor-0_0][INFO]: funasr推理原始结果: <|zh|><|NEUTRAL|><|Speech|><|withitn|>欢迎大家来体验达摩院推出的语音识别模型。
[2025-06-10 14:41:24][ThreadPoolExecutor-0_0][INFO]: funasr处理后结果: 欢迎大家来体验达摩院推出的语音识别模型。
[2025-06-10 14:41:24][ThreadPoolExecutor-0_0][INFO]: funasr语音识别完成 - 总耗时: 0.096秒 | 验证: 0.000秒 | 文件准备: 0.000秒 | 模型推理: 0.096秒 | 清理: 0.000秒
[2025-06-10 14:41:24][ThreadPoolExecutor-0_0][INFO]: 任务 f64f1410-ec68-422c-a373-4278eb959fd9 完成，处理时间: 0.10秒，结果数量: 1 

[2025-06-10 14:41:24][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: 53d6bfde-5b5c-4f46-b562-cdae4f9d830b
[2025-06-10 14:41:24][ThreadPoolExecutor-0_0][INFO]: 任务 53d6bfde-5b5c-4f46-b562-cdae4f9d830b: 音频数量=1, 语言=auto
[2025-06-10 14:41:24][ThreadPoolExecutor-0_0][INFO]: 开始funasr语音识别推理，音频数量: 1, 语言: auto
[2025-06-10 14:41:24][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:41:24][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.002', 'extract_feat': '0.028', 'forward': '0.059', 'batch_size': '1', 'rtf': '0.007'}, : 100%|[34m##########[0m| 1/1 [00:00<00:00, 17.04it/s]
[2025-06-10 14:41:24][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.007: 100%|[34m##########[0m| 1/1 [00:00<00:00, 16.85it/s]
[2025-06-10 14:41:24][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.007: 100%|[34m##########[0m| 1/1 [00:00<00:00, 16.58it/s]
[2025-06-10 14:41:24][ThreadPoolExecutor-0_0][ERROR]: 0%|[31m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:41:24][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:41:24][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:41:24][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.000', 'extract_feat': '0.008', 'forward': '0.070', 'batch_size': '1', 'rtf': '0.009'}, : 100%|[34m##########[0m| 1/1 [00:00<00:00, 14.20it/s]
[2025-06-10 14:41:24][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:41:24][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.009: 100%|[34m##########[0m| 1/1 [00:00<00:00, 14.05it/s]
[2025-06-10 14:41:24][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:41:24][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.009: 100%|[34m##########[0m| 1/1 [00:00<00:00, 13.65it/s]
[2025-06-10 14:41:24][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.010, time_speech:  7.925, time_escape: 0.080: 100%|[31m##########[0m| 1/1 [00:00<00:00, 12.08it/s]
[2025-06-10 14:41:24][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.010, time_speech:  7.925, time_escape: 0.080: 100%|[31m##########[0m| 1/1 [00:00<00:00, 12.00it/s]
[2025-06-10 14:41:24][ThreadPoolExecutor-0_0][INFO]: 处理文本: 国务院发展研究中心市场经济研究所副所长邓玉松认为。
[2025-06-10 14:41:24][ThreadPoolExecutor-0_0][INFO]: funasr推理原始结果: <|zh|><|NEUTRAL|><|Speech|><|withitn|>国务院发展研究中心市场经济研究所副所长邓玉松认为。
[2025-06-10 14:41:24][ThreadPoolExecutor-0_0][INFO]: funasr处理后结果: 国务院发展研究中心市场经济研究所副所长邓玉松认为。
[2025-06-10 14:41:24][ThreadPoolExecutor-0_0][INFO]: funasr语音识别完成 - 总耗时: 0.148秒 | 验证: 0.000秒 | 文件准备: 0.001秒 | 模型推理: 0.147秒 | 清理: 0.000秒
[2025-06-10 14:41:24][ThreadPoolExecutor-0_0][INFO]: 任务 53d6bfde-5b5c-4f46-b562-cdae4f9d830b 完成，处理时间: 0.15秒，结果数量: 1 

[2025-06-10 14:41:24][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: e3325f0b-7843-4bf0-a636-77375054e349
[2025-06-10 14:41:24][ThreadPoolExecutor-0_0][INFO]: 任务 e3325f0b-7843-4bf0-a636-77375054e349: 音频数量=1, 语言=auto
[2025-06-10 14:41:24][ThreadPoolExecutor-0_0][INFO]: 开始funasr语音识别推理，音频数量: 1, 语言: auto
[2025-06-10 14:41:24][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:41:24][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.017', 'extract_feat': '0.009', 'forward': '0.048', 'batch_size': '1', 'rtf': '0.008'}, : 100%|[34m##########[0m| 1/1 [00:00<00:00, 20.91it/s]
[2025-06-10 14:41:24][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.008: 100%|[34m##########[0m| 1/1 [00:00<00:00, 20.75it/s]
[2025-06-10 14:41:24][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.008: 100%|[34m##########[0m| 1/1 [00:00<00:00, 20.44it/s]
[2025-06-10 14:41:24][ThreadPoolExecutor-0_0][ERROR]: 0%|[31m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:41:24][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:41:24][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:41:24][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.000', 'extract_feat': '0.021', 'forward': '0.089', 'batch_size': '1', 'rtf': '0.016'}, : 100%|[34m##########[0m| 1/1 [00:00<00:00, 11.24it/s]
[2025-06-10 14:41:24][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:41:24][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.016: 100%|[34m##########[0m| 1/1 [00:00<00:00, 11.14it/s]
[2025-06-10 14:41:24][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:41:24][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.016: 100%|[34m##########[0m| 1/1 [00:00<00:00, 10.94it/s]
[2025-06-10 14:41:24][ThreadPoolExecutor-0_0][ERROR]: 100%|[31m##########[0m| 1/1 [00:00<00:00,  9.53it/s]
[2025-06-10 14:41:24][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.017, time_speech:  5.848, time_escape: 0.098: 100%|[31m##########[0m| 1/1 [00:00<00:00,  9.53it/s]
[2025-06-10 14:41:24][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.017, time_speech:  5.848, time_escape: 0.098: 100%|[31m##########[0m| 1/1 [00:00<00:00,  9.46it/s]
[2025-06-10 14:41:24][ThreadPoolExecutor-0_0][INFO]: 处理文本: 新洲一名高中新生，因为没去教室上晚自习。
[2025-06-10 14:41:24][ThreadPoolExecutor-0_0][INFO]: funasr推理原始结果: <|zh|><|NEUTRAL|><|Speech|><|withitn|>新洲一名高中新生，因为没去教室上晚自习。
[2025-06-10 14:41:24][ThreadPoolExecutor-0_0][INFO]: funasr处理后结果: 新洲一名高中新生，因为没去教室上晚自习。
[2025-06-10 14:41:24][ThreadPoolExecutor-0_0][INFO]: funasr语音识别完成 - 总耗时: 0.158秒 | 验证: 0.000秒 | 文件准备: 0.001秒 | 模型推理: 0.157秒 | 清理: 0.000秒
[2025-06-10 14:41:24][ThreadPoolExecutor-0_0][INFO]: 任务 e3325f0b-7843-4bf0-a636-77375054e349 完成，处理时间: 0.16秒，结果数量: 1 

[2025-06-10 14:41:24][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: 14bf9e7f-9cb8-4f22-a763-1fe5f47cb192
[2025-06-10 14:41:24][ThreadPoolExecutor-0_0][INFO]: 任务 14bf9e7f-9cb8-4f22-a763-1fe5f47cb192: 音频数量=1, 语言=auto
[2025-06-10 14:41:24][ThreadPoolExecutor-0_0][INFO]: 开始funasr语音识别推理，音频数量: 1, 语言: auto
[2025-06-10 14:41:24][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:41:25][ThreadPoolExecutor-0_0][ERROR]: 100%|[34m##########[0m| 1/1 [00:00<00:00,  2.78it/s]
[2025-06-10 14:41:25][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.020', 'extract_feat': '0.295', 'forward': '0.359', 'batch_size': '1', 'rtf': '0.034'}, : 100%|[34m##########[0m| 1/1 [00:00<00:00,  2.78it/s]
[2025-06-10 14:41:25][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.034: 100%|[34m##########[0m| 1/1 [00:00<00:00,  2.78it/s]
[2025-06-10 14:41:25][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.034: 100%|[34m##########[0m| 1/1 [00:00<00:00,  2.75it/s]
[2025-06-10 14:41:25][ThreadPoolExecutor-0_0][ERROR]: 0%|[31m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:41:25][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/5 [00:00<?, ?it/s]
[2025-06-10 14:41:25][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:41:25][ThreadPoolExecutor-0_0][ERROR]: 100%|[34m##########[0m| 5/5 [00:00<00:00, 18.44it/s]
[2025-06-10 14:41:25][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:41:25][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.000', 'extract_feat': '0.124', 'forward': '0.271', 'batch_size': '5', 'rtf': '0.005'}, : 100%|[34m##########[0m| 5/5 [00:00<00:00, 18.44it/s]
[2025-06-10 14:41:25][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:41:25][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.005: 100%|[34m##########[0m| 5/5 [00:00<00:00, 18.44it/s]
[2025-06-10 14:41:25][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:41:25][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.005: 100%|[34m##########[0m| 5/5 [00:00<00:00, 17.73it/s]
[2025-06-10 14:41:25][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:41:25][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:41:25][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.000', 'extract_feat': '0.012', 'forward': '0.071', 'batch_size': '1', 'rtf': '0.004'}, : 100%|[34m##########[0m| 1/1 [00:00<00:00, 14.06it/s]
[2025-06-10 14:41:25][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:41:25][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004: 100%|[34m##########[0m| 1/1 [00:00<00:00, 13.92it/s]
[2025-06-10 14:41:25][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:41:25][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004: 100%|[34m##########[0m| 1/1 [00:00<00:00, 13.30it/s]
[2025-06-10 14:41:25][ThreadPoolExecutor-0_0][ERROR]: 100%|[31m##########[0m| 1/1 [00:00<00:00,  2.58it/s]
[2025-06-10 14:41:25][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.005, time_speech:  70.471, time_escape: 0.371: 100%|[31m##########[0m| 1/1 [00:00<00:00,  2.58it/s]
[2025-06-10 14:41:25][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.005, time_speech:  70.471, time_escape: 0.371: 100%|[31m##########[0m| 1/1 [00:00<00:00,  2.58it/s]
[2025-06-10 14:41:25][ThreadPoolExecutor-0_0][INFO]: 处理文本: 试错的过程很简单而，且特别是今天报名仓雪卡的同学，你们可以。听到后面的有专门的活动课，他会大大降低你的试绸成本。其实你也可以过来听课，为什么你自己写嘛？我先今天写5个点，我就试试试验一下，反正这5个点不行，我再写5个点，这试再不行，那再写5个点嘛。你总会所谓的活动搭神和所谓的高手，都是只有一个把所有的错，所有的坑全部趟一遍，留下正确的你就是所谓的搭神。明白吗？所以说关于活动通过这块，我只送给你们四个字啊，换位思考。如果说你要想降低你的试错成本，今天来这里你们就是对的。因为有畅畅血卡这个机会，所以说关于活动过于不过这个问题，或者活动很难通过这个话题呃，如果真的要坐下来聊的话，要聊一天。😡但是我觉得我刚才说的四个字足够。好，谢谢。好，非常感谢那个三茂老师的回答啊。三茂老师说，我们在整个店铺的这个活动当中，我们要学会换位思考。其实。😊
[2025-06-10 14:41:25][ThreadPoolExecutor-0_0][INFO]: funasr推理原始结果: <|zh|><|ANGRY|><|Speech|><|withitn|>试错的过程很简单而，且特别是今天报名仓雪卡的同学，你们可以。 <|zh|><|ANGRY|><|Speech|><|withitn|>听到后面的有专门的活动课，他会大大降低你的试绸成本。其实你也可以过来听课，为什么你自己写嘛？我先今天写5个点，我就试试试验一下，反正这5个点不行，我再写5个点，这试再不行，那再写5个点嘛。 <|zh|><|ANGRY|><|Speech|><|withitn|>你总会所谓的活动搭神和所谓的高手，都是只有一个把所有的错，所有的坑全部趟一遍，留下正确的你就是所谓的搭神。 <|zh|><|ANGRY|><|Speech|><|withitn|>明白吗？所以说关于活动通过这块，我只送给你们四个字啊，换位思考。如果说你要想降低你的试错成本，今天来这里你们就是对的。 <|zh|><|ANGRY|><|Speech|><|withitn|>因为有畅畅血卡这个机会，所以说关于活动过于不过这个问题，或者活动很难通过这个话题呃，如果真的要坐下来聊的话，要聊一天。 <|zh|><|HAPPY|><|Speech|><|withitn|>但是我觉得我刚才说的四个字足够。好，谢谢。好，非常感谢那个三茂老师的回答啊。三茂老师说，我们在整个店铺的这个活动当中，我们要学会换位思考。其实。
[2025-06-10 14:41:25][ThreadPoolExecutor-0_0][INFO]: funasr处理后结果: 试错的过程很简单而，且特别是今天报名仓雪卡的同学，你们可以。听到后面的有专门的活动课，他会大大降低你的试绸成本。其实你也可以过来听课，为什么你自己写嘛？我先今天写5个点，我就试试试验一下，反正这5个点不行，我再写5个点，这试再不行，那再写5个点嘛。你总会所谓的活动搭神和所谓的高手，都是只有一个把所有的错，所有的坑全部趟一遍，留下正确的你就是所谓的搭神。明白吗？所以说关于活动通过这块，我只送给你们四个字啊，换位思考。如果说你要想降低你的试错成本，今天来这里你们就是对的。因为有畅畅血卡这个机会，所以说关于活动过于不过这个问题，或者活动很难通过这个话题呃，如果真的要坐下来聊的话，要聊一天。😡但是我觉得我刚才说的四个字足够。好，谢谢。好，非常感谢那个三茂老师的回答啊。三茂老师说，我们在整个店铺的这个活动当中，我们要学会换位思考。其实。😊
[2025-06-10 14:41:25][ThreadPoolExecutor-0_0][INFO]: funasr语音识别完成 - 总耗时: 0.759秒 | 验证: 0.000秒 | 文件准备: 0.003秒 | 模型推理: 0.755秒 | 清理: 0.001秒
[2025-06-10 14:41:25][ThreadPoolExecutor-0_0][INFO]: 任务 14bf9e7f-9cb8-4f22-a763-1fe5f47cb192 完成，处理时间: 0.76秒，结果数量: 1 

[2025-06-10 14:41:25][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: 0fc4bdb9-a9f8-4f51-83f8-9b4adf09c90a
[2025-06-10 14:41:25][ThreadPoolExecutor-0_0][INFO]: 任务 0fc4bdb9-a9f8-4f51-83f8-9b4adf09c90a: 音频数量=5, 语言=auto
[2025-06-10 14:41:25][ThreadPoolExecutor-0_0][INFO]: 开始funasr语音识别推理，音频数量: 5, 语言: auto
[2025-06-10 14:41:25][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:41:25][ThreadPoolExecutor-0_0][ERROR]: 100%|[34m##########[0m| 1/1 [00:00<00:00,  8.72it/s]
[2025-06-10 14:41:25][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.071', 'extract_feat': '0.008', 'forward': '0.115', 'batch_size': '1', 'rtf': '0.008'}, : 100%|[34m##########[0m| 1/1 [00:00<00:00,  8.72it/s]
[2025-06-10 14:41:25][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.008: 100%|[34m##########[0m| 1/1 [00:00<00:00,  8.72it/s]
[2025-06-10 14:41:25][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.008: 100%|[34m##########[0m| 1/1 [00:00<00:00,  8.56it/s]
[2025-06-10 14:41:25][ThreadPoolExecutor-0_0][ERROR]: 0%|[31m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:41:25][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:41:25][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:41:25][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.000', 'extract_feat': '0.009', 'forward': '0.066', 'batch_size': '1', 'rtf': '0.006'}, : 100%|[34m##########[0m| 1/1 [00:00<00:00, 15.15it/s]
[2025-06-10 14:41:25][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:41:25][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.006: 100%|[34m##########[0m| 1/1 [00:00<00:00, 15.00it/s]
[2025-06-10 14:41:25][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:41:25][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.006: 100%|[34m##########[0m| 1/1 [00:00<00:00, 14.56it/s]
[2025-06-10 14:41:25][ThreadPoolExecutor-0_0][ERROR]: 100%|[31m##########[0m| 1/1 [00:00<00:00,  7.31it/s]
[2025-06-10 14:41:25][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.005, time_speech:  13.704, time_escape: 0.073: 100%|[31m##########[0m| 1/1 [00:00<00:00,  7.31it/s]
[2025-06-10 14:41:25][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.005, time_speech:  13.704, time_escape: 0.073: 100%|[31m##########[0m| 1/1 [00:00<00:00,  7.28it/s]
[2025-06-10 14:41:25][ThreadPoolExecutor-0_0][INFO]: 处理文本: 南晚会时项目标计算法数据标注钢精测计算法测试数据标注。
[2025-06-10 14:41:25][ThreadPoolExecutor-0_0][INFO]: funasr推理原始结果: <|zh|><|NEUTRAL|><|Speech|><|withitn|>南晚会时项目标计算法数据标注钢精测计算法测试数据标注。
[2025-06-10 14:41:25][ThreadPoolExecutor-0_0][INFO]: funasr处理后结果: 南晚会时项目标计算法数据标注钢精测计算法测试数据标注。
[2025-06-10 14:41:25][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:41:25][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.001', 'extract_feat': '0.004', 'forward': '0.021', 'batch_size': '1', 'rtf': '0.004'}, : 100%|[34m##########[0m| 1/1 [00:00<00:00, 47.50it/s]
[2025-06-10 14:41:25][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004: 100%|[34m##########[0m| 1/1 [00:00<00:00, 46.56it/s]
[2025-06-10 14:41:25][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004: 100%|[34m##########[0m| 1/1 [00:00<00:00, 45.16it/s]
[2025-06-10 14:41:25][ThreadPoolExecutor-0_0][ERROR]: 0%|[31m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:41:25][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:41:25][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:41:26][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.000', 'extract_feat': '0.005', 'forward': '0.070', 'batch_size': '1', 'rtf': '0.014'}, : 100%|[34m##########[0m| 1/1 [00:00<00:00, 14.25it/s]
[2025-06-10 14:41:26][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:41:26][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.014: 100%|[34m##########[0m| 1/1 [00:00<00:00, 14.12it/s]
[2025-06-10 14:41:26][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:41:26][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.014: 100%|[34m##########[0m| 1/1 [00:00<00:00, 13.86it/s]
[2025-06-10 14:41:26][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.014, time_speech:  5.547, time_escape: 0.077: 100%|[31m##########[0m| 1/1 [00:00<00:00, 12.85it/s]
[2025-06-10 14:41:26][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.014, time_speech:  5.547, time_escape: 0.077: 100%|[31m##########[0m| 1/1 [00:00<00:00, 12.80it/s]
[2025-06-10 14:41:26][ThreadPoolExecutor-0_0][INFO]: 处理文本: 欢迎大家来体验达摩院推出的语音识别模型。
[2025-06-10 14:41:26][ThreadPoolExecutor-0_0][INFO]: funasr推理原始结果: <|zh|><|NEUTRAL|><|Speech|><|withitn|>欢迎大家来体验达摩院推出的语音识别模型。
[2025-06-10 14:41:26][ThreadPoolExecutor-0_0][INFO]: funasr处理后结果: 欢迎大家来体验达摩院推出的语音识别模型。
[2025-06-10 14:41:26][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:41:26][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.001', 'extract_feat': '0.004', 'forward': '0.033', 'batch_size': '1', 'rtf': '0.004'}, : 100%|[34m##########[0m| 1/1 [00:00<00:00, 30.45it/s]
[2025-06-10 14:41:26][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004: 100%|[34m##########[0m| 1/1 [00:00<00:00, 29.96it/s]
[2025-06-10 14:41:26][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004: 100%|[34m##########[0m| 1/1 [00:00<00:00, 29.12it/s]
[2025-06-10 14:41:26][ThreadPoolExecutor-0_0][ERROR]: 0%|[31m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:41:26][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:41:26][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:41:26][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.000', 'extract_feat': '0.007', 'forward': '0.064', 'batch_size': '1', 'rtf': '0.008'}, : 100%|[34m##########[0m| 1/1 [00:00<00:00, 15.52it/s]
[2025-06-10 14:41:26][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:41:26][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.008: 100%|[34m##########[0m| 1/1 [00:00<00:00, 15.38it/s]
[2025-06-10 14:41:26][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:41:26][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.008: 100%|[34m##########[0m| 1/1 [00:00<00:00, 15.03it/s]
[2025-06-10 14:41:26][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.009, time_speech:  7.925, time_escape: 0.072: 100%|[31m##########[0m| 1/1 [00:00<00:00, 13.51it/s]
[2025-06-10 14:41:26][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.009, time_speech:  7.925, time_escape: 0.072: 100%|[31m##########[0m| 1/1 [00:00<00:00, 13.47it/s]
[2025-06-10 14:41:26][ThreadPoolExecutor-0_0][INFO]: 处理文本: 国务院发展研究中心市场经济研究所副所长邓玉松认为。
[2025-06-10 14:41:26][ThreadPoolExecutor-0_0][INFO]: funasr推理原始结果: <|zh|><|NEUTRAL|><|Speech|><|withitn|>国务院发展研究中心市场经济研究所副所长邓玉松认为。
[2025-06-10 14:41:26][ThreadPoolExecutor-0_0][INFO]: funasr处理后结果: 国务院发展研究中心市场经济研究所副所长邓玉松认为。
[2025-06-10 14:41:26][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:41:26][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.001', 'extract_feat': '0.003', 'forward': '0.024', 'batch_size': '1', 'rtf': '0.004'}, : 100%|[34m##########[0m| 1/1 [00:00<00:00, 42.29it/s]
[2025-06-10 14:41:26][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004: 100%|[34m##########[0m| 1/1 [00:00<00:00, 41.61it/s]
[2025-06-10 14:41:26][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004: 100%|[34m##########[0m| 1/1 [00:00<00:00, 40.37it/s]
[2025-06-10 14:41:26][ThreadPoolExecutor-0_0][ERROR]: 0%|[31m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:41:26][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:41:26][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:41:26][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.000', 'extract_feat': '0.004', 'forward': '0.056', 'batch_size': '1', 'rtf': '0.010'}, : 100%|[34m##########[0m| 1/1 [00:00<00:00, 17.92it/s]
[2025-06-10 14:41:26][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:41:26][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.010: 100%|[34m##########[0m| 1/1 [00:00<00:00, 17.74it/s]
[2025-06-10 14:41:26][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:41:26][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.010: 100%|[34m##########[0m| 1/1 [00:00<00:00, 17.32it/s]
[2025-06-10 14:41:26][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.011, time_speech:  5.848, time_escape: 0.062: 100%|[31m##########[0m| 1/1 [00:00<00:00, 15.84it/s]
[2025-06-10 14:41:26][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.011, time_speech:  5.848, time_escape: 0.062: 100%|[31m##########[0m| 1/1 [00:00<00:00, 15.76it/s]
[2025-06-10 14:41:26][ThreadPoolExecutor-0_0][INFO]: 处理文本: 新洲一名高中新生，因为没去教室上晚自习。
[2025-06-10 14:41:26][ThreadPoolExecutor-0_0][INFO]: funasr推理原始结果: <|zh|><|NEUTRAL|><|Speech|><|withitn|>新洲一名高中新生，因为没去教室上晚自习。
[2025-06-10 14:41:26][ThreadPoolExecutor-0_0][INFO]: funasr处理后结果: 新洲一名高中新生，因为没去教室上晚自习。
[2025-06-10 14:41:26][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:41:26][ThreadPoolExecutor-0_0][ERROR]: 100%|[34m##########[0m| 1/1 [00:00<00:00,  3.27it/s]
[2025-06-10 14:41:26][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.009', 'extract_feat': '0.263', 'forward': '0.306', 'batch_size': '1', 'rtf': '0.029'}, : 100%|[34m##########[0m| 1/1 [00:00<00:00,  3.27it/s]
[2025-06-10 14:41:26][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.029: 100%|[34m##########[0m| 1/1 [00:00<00:00,  3.27it/s]
[2025-06-10 14:41:26][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.029: 100%|[34m##########[0m| 1/1 [00:00<00:00,  3.23it/s]
[2025-06-10 14:41:26][ThreadPoolExecutor-0_0][ERROR]: 0%|[31m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:41:26][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/5 [00:00<?, ?it/s]
[2025-06-10 14:41:26][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:41:26][ThreadPoolExecutor-0_0][ERROR]: 100%|[34m##########[0m| 5/5 [00:00<00:00, 18.64it/s]
[2025-06-10 14:41:26][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:41:26][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.000', 'extract_feat': '0.119', 'forward': '0.268', 'batch_size': '5', 'rtf': '0.005'}, : 100%|[34m##########[0m| 5/5 [00:00<00:00, 18.64it/s]
[2025-06-10 14:41:26][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:41:26][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.005: 100%|[34m##########[0m| 5/5 [00:00<00:00, 18.64it/s]
[2025-06-10 14:41:26][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:41:26][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.005: 100%|[34m##########[0m| 5/5 [00:00<00:00, 17.87it/s]
[2025-06-10 14:41:26][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:41:26][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:41:26][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.000', 'extract_feat': '0.014', 'forward': '0.081', 'batch_size': '1', 'rtf': '0.005'}, : 100%|[34m##########[0m| 1/1 [00:00<00:00, 12.38it/s]
[2025-06-10 14:41:26][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:41:26][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.005: 100%|[34m##########[0m| 1/1 [00:00<00:00, 12.30it/s]
[2025-06-10 14:41:26][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:41:26][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.005: 100%|[34m##########[0m| 1/1 [00:00<00:00, 11.86it/s]
[2025-06-10 14:41:26][ThreadPoolExecutor-0_0][ERROR]: 100%|[31m##########[0m| 1/1 [00:00<00:00,  2.59it/s]
[2025-06-10 14:41:26][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.005, time_speech:  70.471, time_escape: 0.376: 100%|[31m##########[0m| 1/1 [00:00<00:00,  2.59it/s]
[2025-06-10 14:41:26][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.005, time_speech:  70.471, time_escape: 0.376: 100%|[31m##########[0m| 1/1 [00:00<00:00,  2.59it/s]
[2025-06-10 14:41:26][ThreadPoolExecutor-0_0][INFO]: 处理文本: 试错的过程很简单而，且特别是今天报名仓雪卡的同学，你们可以。听到后面的有专门的活动课，他会大大降低你的试绸成本。其实你也可以过来听课，为什么你自己写嘛？我先今天写5个点，我就试试试验一下，反正这5个点不行，我再写5个点，这试再不行，那再写5个点嘛。你总会所谓的活动搭神和所谓的高手，都是只有一个把所有的错，所有的坑全部趟一遍，留下正确的你就是所谓的搭神。明白吗？所以说关于活动通过这块，我只送给你们四个字啊，换位思考。如果说你要想降低你的试错成本，今天来这里你们就是对的。因为有畅血畅血卡这个机会，所以说关于活动过于不过这个问题，或者活动很难通过这个话题呃，如果真的要坐下来聊的话，要聊一天。😡但是我觉得我刚才说的四个字足够。好，谢谢。好，非常感谢那个三茂老师的回答啊。三茂老师说，我们在整个店铺的这个活动当中，我们要学会换位思考。其实。😊
[2025-06-10 14:41:26][ThreadPoolExecutor-0_0][INFO]: funasr推理原始结果: <|zh|><|ANGRY|><|Speech|><|withitn|>试错的过程很简单而，且特别是今天报名仓雪卡的同学，你们可以。 <|zh|><|ANGRY|><|Speech|><|withitn|>听到后面的有专门的活动课，他会大大降低你的试绸成本。其实你也可以过来听课，为什么你自己写嘛？我先今天写5个点，我就试试试验一下，反正这5个点不行，我再写5个点，这试再不行，那再写5个点嘛。 <|zh|><|ANGRY|><|Speech|><|withitn|>你总会所谓的活动搭神和所谓的高手，都是只有一个把所有的错，所有的坑全部趟一遍，留下正确的你就是所谓的搭神。 <|zh|><|ANGRY|><|Speech|><|withitn|>明白吗？所以说关于活动通过这块，我只送给你们四个字啊，换位思考。如果说你要想降低你的试错成本，今天来这里你们就是对的。 <|zh|><|ANGRY|><|Speech|><|withitn|>因为有畅血畅血卡这个机会，所以说关于活动过于不过这个问题，或者活动很难通过这个话题呃，如果真的要坐下来聊的话，要聊一天。 <|zh|><|HAPPY|><|Speech|><|withitn|>但是我觉得我刚才说的四个字足够。好，谢谢。好，非常感谢那个三茂老师的回答啊。三茂老师说，我们在整个店铺的这个活动当中，我们要学会换位思考。其实。
[2025-06-10 14:41:26][ThreadPoolExecutor-0_0][INFO]: funasr处理后结果: 试错的过程很简单而，且特别是今天报名仓雪卡的同学，你们可以。听到后面的有专门的活动课，他会大大降低你的试绸成本。其实你也可以过来听课，为什么你自己写嘛？我先今天写5个点，我就试试试验一下，反正这5个点不行，我再写5个点，这试再不行，那再写5个点嘛。你总会所谓的活动搭神和所谓的高手，都是只有一个把所有的错，所有的坑全部趟一遍，留下正确的你就是所谓的搭神。明白吗？所以说关于活动通过这块，我只送给你们四个字啊，换位思考。如果说你要想降低你的试错成本，今天来这里你们就是对的。因为有畅血畅血卡这个机会，所以说关于活动过于不过这个问题，或者活动很难通过这个话题呃，如果真的要坐下来聊的话，要聊一天。😡但是我觉得我刚才说的四个字足够。好，谢谢。好，非常感谢那个三茂老师的回答啊。三茂老师说，我们在整个店铺的这个活动当中，我们要学会换位思考。其实。😊
[2025-06-10 14:41:26][ThreadPoolExecutor-0_0][INFO]: funasr语音识别完成 - 总耗时: 1.266秒 | 验证: 0.000秒 | 文件准备: 0.005秒 | 模型推理: 1.260秒 | 清理: 0.001秒
[2025-06-10 14:41:26][ThreadPoolExecutor-0_0][INFO]: 任务 0fc4bdb9-a9f8-4f51-83f8-9b4adf09c90a 完成，处理时间: 1.27秒，结果数量: 5 

[2025-06-10 14:42:13][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: 326c0a02-8dfc-45f8-8839-b8ce36309859
[2025-06-10 14:42:13][ThreadPoolExecutor-0_0][INFO]: 任务 326c0a02-8dfc-45f8-8839-b8ce36309859: 音频数量=1, 语言=zh
[2025-06-10 14:42:13][ThreadPoolExecutor-0_0][INFO]: 开始funasr语音识别推理，音频数量: 1, 语言: zh
[2025-06-10 14:42:13][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:42:13][ThreadPoolExecutor-0_0][ERROR]: 100%|[34m##########[0m| 1/1 [00:00<00:00,  3.00it/s]
[2025-06-10 14:42:13][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.011', 'extract_feat': '0.283', 'forward': '0.333', 'batch_size': '1', 'rtf': '0.032'}, : 100%|[34m##########[0m| 1/1 [00:00<00:00,  3.00it/s]
[2025-06-10 14:42:13][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.032: 100%|[34m##########[0m| 1/1 [00:00<00:00,  3.00it/s]
[2025-06-10 14:42:13][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.032: 100%|[34m##########[0m| 1/1 [00:00<00:00,  2.95it/s]
[2025-06-10 14:42:13][ThreadPoolExecutor-0_0][ERROR]: 0%|[31m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:42:13][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/5 [00:00<?, ?it/s]
[2025-06-10 14:42:13][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:42:13][ThreadPoolExecutor-0_0][ERROR]: 100%|[34m##########[0m| 5/5 [00:00<00:00, 23.13it/s]
[2025-06-10 14:42:13][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:42:13][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.000', 'extract_feat': '0.047', 'forward': '0.216', 'batch_size': '5', 'rtf': '0.004'}, : 100%|[34m##########[0m| 5/5 [00:00<00:00, 23.13it/s]
[2025-06-10 14:42:13][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:42:13][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004: 100%|[34m##########[0m| 5/5 [00:00<00:00, 23.13it/s]
[2025-06-10 14:42:13][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:42:13][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004: 100%|[34m##########[0m| 5/5 [00:00<00:00, 21.88it/s]
[2025-06-10 14:42:13][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:42:13][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:42:13][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.000', 'extract_feat': '0.012', 'forward': '0.074', 'batch_size': '1', 'rtf': '0.004'}, : 100%|[34m##########[0m| 1/1 [00:00<00:00, 13.44it/s]
[2025-06-10 14:42:13][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:42:13][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004: 100%|[34m##########[0m| 1/1 [00:00<00:00, 13.33it/s]
[2025-06-10 14:42:13][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:42:13][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004: 100%|[34m##########[0m| 1/1 [00:00<00:00, 12.75it/s]
[2025-06-10 14:42:13][ThreadPoolExecutor-0_0][ERROR]: 100%|[31m##########[0m| 1/1 [00:00<00:00,  3.03it/s]
[2025-06-10 14:42:13][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.005, time_speech:  70.471, time_escape: 0.318: 100%|[31m##########[0m| 1/1 [00:00<00:00,  3.03it/s]
[2025-06-10 14:42:13][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.005, time_speech:  70.471, time_escape: 0.318: 100%|[31m##########[0m| 1/1 [00:00<00:00,  3.03it/s]
[2025-06-10 14:42:13][ThreadPoolExecutor-0_0][INFO]: 处理文本: 试错的过程很简单而且特别是今天报名仓雪卡的同学你们可以听到后面的有专门的活动课他会大大降低你的试错成本其实你也可以过来听课为什么你自己写嘛我先今天写五个点我就试试试验一下反正这五个点不行我再写五个点这试再不行那再写五个点嘛你总会所谓的活动搭神和所谓的高手都是只有一个把所有的错所有的坑全部趟一遍留下正确的你就是所谓的搭神明白吗所以说关于活动通过这一块我只送给你们四个字啊换位思考如果说你要想降低你的试错成本今天来这里你们就是对的因为有畅畅血卡这个机会所以说关于活动过于不过这个问题或者活动很难通过这个话题呃如果真的要坐下来聊的话要聊一天😡但是我觉得我刚才说的四个字足够好谢谢好非常感谢那个三茂老师的回答啊三茂老师说我们在整个店铺的这个活动当中我们要学会换位思考其实😊
[2025-06-10 14:42:13][ThreadPoolExecutor-0_0][INFO]: funasr推理原始结果: <|zh|><|ANGRY|><|Speech|><|woitn|>试错的过程很简单而且特别是今天报名仓雪卡的同学你们可以 <|zh|><|ANGRY|><|Speech|><|woitn|>听到后面的有专门的活动课他会大大降低你的试错成本其实你也可以过来听课为什么你自己写嘛我先今天写五个点我就试试试验一下反正这五个点不行我再写五个点这试再不行那再写五个点嘛 <|zh|><|ANGRY|><|Speech|><|woitn|>你总会所谓的活动搭神和所谓的高手都是只有一个把所有的错所有的坑全部趟一遍留下正确的你就是所谓的搭神 <|zh|><|ANGRY|><|Speech|><|woitn|>明白吗所以说关于活动通过这一块我只送给你们四个字啊换位思考如果说你要想降低你的试错成本今天来这里你们就是对的 <|zh|><|ANGRY|><|Speech|><|woitn|>因为有畅畅血卡这个机会所以说关于活动过于不过这个问题或者活动很难通过这个话题呃如果真的要坐下来聊的话要聊一天 <|zh|><|HAPPY|><|Speech|><|woitn|>但是我觉得我刚才说的四个字足够好谢谢好非常感谢那个三茂老师的回答啊三茂老师说我们在整个店铺的这个活动当中我们要学会换位思考其实
[2025-06-10 14:42:13][ThreadPoolExecutor-0_0][INFO]: funasr处理后结果: 试错的过程很简单而且特别是今天报名仓雪卡的同学你们可以听到后面的有专门的活动课他会大大降低你的试错成本其实你也可以过来听课为什么你自己写嘛我先今天写五个点我就试试试验一下反正这五个点不行我再写五个点这试再不行那再写五个点嘛你总会所谓的活动搭神和所谓的高手都是只有一个把所有的错所有的坑全部趟一遍留下正确的你就是所谓的搭神明白吗所以说关于活动通过这一块我只送给你们四个字啊换位思考如果说你要想降低你的试错成本今天来这里你们就是对的因为有畅畅血卡这个机会所以说关于活动过于不过这个问题或者活动很难通过这个话题呃如果真的要坐下来聊的话要聊一天😡但是我觉得我刚才说的四个字足够好谢谢好非常感谢那个三茂老师的回答啊三茂老师说我们在整个店铺的这个活动当中我们要学会换位思考其实😊
[2025-06-10 14:42:13][ThreadPoolExecutor-0_0][INFO]: funasr语音识别完成 - 总耗时: 0.675秒 | 验证: 0.000秒 | 文件准备: 0.003秒 | 模型推理: 0.672秒 | 清理: 0.000秒
[2025-06-10 14:42:13][ThreadPoolExecutor-0_0][INFO]: 任务 326c0a02-8dfc-45f8-8839-b8ce36309859 完成，处理时间: 0.68秒，结果数量: 1 

[2025-06-10 14:42:13][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: 51bdedab-65df-4f42-8274-31ad0ed0902d
[2025-06-10 14:42:13][ThreadPoolExecutor-0_0][INFO]: 任务 51bdedab-65df-4f42-8274-31ad0ed0902d: 音频数量=1, 语言=zh
[2025-06-10 14:42:13][ThreadPoolExecutor-0_0][INFO]: 开始funasr语音识别推理，音频数量: 1, 语言: zh
[2025-06-10 14:42:13][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:42:14][ThreadPoolExecutor-0_0][ERROR]: 100%|[34m##########[0m| 1/1 [00:00<00:00,  3.48it/s]
[2025-06-10 14:42:14][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.010', 'extract_feat': '0.251', 'forward': '0.288', 'batch_size': '1', 'rtf': '0.027'}, : 100%|[34m##########[0m| 1/1 [00:00<00:00,  3.48it/s]
[2025-06-10 14:42:14][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.027: 100%|[34m##########[0m| 1/1 [00:00<00:00,  3.48it/s]
[2025-06-10 14:42:14][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.027: 100%|[34m##########[0m| 1/1 [00:00<00:00,  3.43it/s]
[2025-06-10 14:42:14][ThreadPoolExecutor-0_0][ERROR]: 0%|[31m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:42:14][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/5 [00:00<?, ?it/s]
[2025-06-10 14:42:14][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:42:14][ThreadPoolExecutor-0_0][ERROR]: 100%|[34m##########[0m| 5/5 [00:00<00:00, 25.78it/s]
[2025-06-10 14:42:14][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:42:14][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.000', 'extract_feat': '0.050', 'forward': '0.194', 'batch_size': '5', 'rtf': '0.004'}, : 100%|[34m##########[0m| 5/5 [00:00<00:00, 25.78it/s]
[2025-06-10 14:42:14][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:42:14][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004: 100%|[34m##########[0m| 5/5 [00:00<00:00, 25.78it/s]
[2025-06-10 14:42:14][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:42:14][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004: 100%|[34m##########[0m| 5/5 [00:00<00:00, 23.12it/s]
[2025-06-10 14:42:14][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:42:14][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:42:14][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.000', 'extract_feat': '0.012', 'forward': '0.080', 'batch_size': '1', 'rtf': '0.005'}, : 100%|[34m##########[0m| 1/1 [00:00<00:00, 12.46it/s]
[2025-06-10 14:42:14][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:42:14][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.005: 100%|[34m##########[0m| 1/1 [00:00<00:00, 12.35it/s]
[2025-06-10 14:42:14][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:42:14][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.005: 100%|[34m##########[0m| 1/1 [00:00<00:00, 11.86it/s]
[2025-06-10 14:42:14][ThreadPoolExecutor-0_0][ERROR]: 100%|[31m##########[0m| 1/1 [00:00<00:00,  3.12it/s]
[2025-06-10 14:42:14][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004, time_speech:  70.471, time_escape: 0.312: 100%|[31m##########[0m| 1/1 [00:00<00:00,  3.12it/s]
[2025-06-10 14:42:14][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004, time_speech:  70.471, time_escape: 0.312: 100%|[31m##########[0m| 1/1 [00:00<00:00,  3.11it/s]
[2025-06-10 14:42:14][ThreadPoolExecutor-0_0][INFO]: 处理文本: 试错的过程很简单而，且特别是今天报名仓雪卡的同学，你们可以。听到后面的有专门的活动课，他会大大降低你的试错成本。其实你也可以过来听课，为什么你自己写嘛？我先今天写5个点，我就试试试验一下，反正这5个点不行，我再写5个点，这试再不行，那再写5个点嘛。你总会所谓的活动搭神和所谓的高手，都是只有一个把所有的错，所有的坑全部趟一遍，留下正确的你就是所谓的搭神。明白吗？所以说关于活动通过这块，我只送给你们四个字啊，换位思考。如果说你要想降低你的试错成本，今天来这里你们就是对的。因为有畅畅血卡这个机会，所以说关于活动过于不过这个问题，或者活动很难通过这个话题呃，如果真的要坐下来聊的话，要聊一天。😡但是我觉得我刚才说的四个字足够。好，谢谢。好，非常感谢那个三茂老师的回答啊。三茂老师说，我们在整个店铺的这个活动当中，我们要学会换位思考。其实。😊
[2025-06-10 14:42:14][ThreadPoolExecutor-0_0][INFO]: funasr推理原始结果: <|zh|><|ANGRY|><|Speech|><|withitn|>试错的过程很简单而，且特别是今天报名仓雪卡的同学，你们可以。 <|zh|><|ANGRY|><|Speech|><|withitn|>听到后面的有专门的活动课，他会大大降低你的试错成本。其实你也可以过来听课，为什么你自己写嘛？我先今天写5个点，我就试试试验一下，反正这5个点不行，我再写5个点，这试再不行，那再写5个点嘛。 <|zh|><|ANGRY|><|Speech|><|withitn|>你总会所谓的活动搭神和所谓的高手，都是只有一个把所有的错，所有的坑全部趟一遍，留下正确的你就是所谓的搭神。 <|zh|><|ANGRY|><|Speech|><|withitn|>明白吗？所以说关于活动通过这块，我只送给你们四个字啊，换位思考。如果说你要想降低你的试错成本，今天来这里你们就是对的。 <|zh|><|ANGRY|><|Speech|><|withitn|>因为有畅畅血卡这个机会，所以说关于活动过于不过这个问题，或者活动很难通过这个话题呃，如果真的要坐下来聊的话，要聊一天。 <|zh|><|HAPPY|><|Speech|><|withitn|>但是我觉得我刚才说的四个字足够。好，谢谢。好，非常感谢那个三茂老师的回答啊。三茂老师说，我们在整个店铺的这个活动当中，我们要学会换位思考。其实。
[2025-06-10 14:42:14][ThreadPoolExecutor-0_0][INFO]: funasr处理后结果: 试错的过程很简单而，且特别是今天报名仓雪卡的同学，你们可以。听到后面的有专门的活动课，他会大大降低你的试错成本。其实你也可以过来听课，为什么你自己写嘛？我先今天写5个点，我就试试试验一下，反正这5个点不行，我再写5个点，这试再不行，那再写5个点嘛。你总会所谓的活动搭神和所谓的高手，都是只有一个把所有的错，所有的坑全部趟一遍，留下正确的你就是所谓的搭神。明白吗？所以说关于活动通过这块，我只送给你们四个字啊，换位思考。如果说你要想降低你的试错成本，今天来这里你们就是对的。因为有畅畅血卡这个机会，所以说关于活动过于不过这个问题，或者活动很难通过这个话题呃，如果真的要坐下来聊的话，要聊一天。😡但是我觉得我刚才说的四个字足够。好，谢谢。好，非常感谢那个三茂老师的回答啊。三茂老师说，我们在整个店铺的这个活动当中，我们要学会换位思考。其实。😊
[2025-06-10 14:42:14][ThreadPoolExecutor-0_0][INFO]: funasr语音识别完成 - 总耗时: 0.619秒 | 验证: 0.000秒 | 文件准备: 0.002秒 | 模型推理: 0.615秒 | 清理: 0.001秒
[2025-06-10 14:42:14][ThreadPoolExecutor-0_0][INFO]: 任务 51bdedab-65df-4f42-8274-31ad0ed0902d 完成，处理时间: 0.62秒，结果数量: 1 

[2025-06-10 14:54:40][MainThread][INFO]: ==================================================
[2025-06-10 14:54:40][MainThread][INFO]: 启动语音识别gRPC服务
[2025-06-10 14:54:40][MainThread][INFO]: ==================================================
[2025-06-10 14:54:40][MainThread][INFO]: 初始化语音识别推理引擎...
[2025-06-10 14:54:40][MainThread][INFO]: 推理模式: funasr
[2025-06-10 14:54:40][MainThread][INFO]: 使用FUNASR模式 - funasr AutoModel (支持更好的断句)
[2025-06-10 14:54:43][MainThread][INFO]: 开始加载funasr语音识别模型，模型路径: ./SenseVoice_model
[2025-06-10 14:54:43][MainThread][INFO]: 使用设备: cuda:0
[2025-06-10 14:54:43][MainThread][INFO]: funasr version: 1.2.6.
[2025-06-10 14:54:43][MainThread][INFO]: Check update of funasr, and it would cost few times. You may disable it by set `disable_update=True` in AutoModel
[2025-06-10 14:54:44][MainThread][INFO]: You are using the latest version of funasr-1.2.6
[2025-06-10 14:54:44][MainThread][INFO]: Loading remote code successfully: ./model.py
[2025-06-10 14:54:51][MainThread][INFO]: Downloading Model from https://www.modelscope.cn to directory: /home/<USER>/.cache/modelscope/hub/models/iic/speech_fsmn_vad_zh-cn-16k-common-pytorch
[2025-06-10 14:54:51][MainThread][ERROR]: 2025-06-10 14:54:51,436 - modelscope - WARNING - Using branch: master as version is unstable, use with caution
[2025-06-10 14:54:51][MainThread][INFO]: funasr模型加载成功，耗时: 8.22秒
[2025-06-10 14:54:51][MainThread][INFO]: 推理引擎加载成功，模式: funasr
[2025-06-10 14:54:51][MainThread][INFO]: 推理引擎信息: funasr AutoModel - 断句效果好，更贴近对话，但需要联网
[2025-06-10 14:54:51][MainThread][INFO]: 语音识别推理引擎初始化成功
[2025-06-10 14:54:51][MainThread][INFO]: 服务名称: speech_recognition
[2025-06-10 14:54:51][MainThread][INFO]: 监听地址: 0.0.0.0:50051
[2025-06-10 14:54:51][MainThread][INFO]: 模型路径: ./SenseVoice_model
[2025-06-10 14:54:51][MainThread][INFO]: 推理设备: cuda:0
[2025-06-10 14:54:51][MainThread][INFO]: 推理模式: funasr
[2025-06-10 14:54:51][MainThread][INFO]: 最大工作线程: 20
[2025-06-10 14:54:51][MainThread][INFO]: 最大消息大小: 50.0MB
[2025-06-10 14:54:51][MainThread][INFO]: 语音识别gRPC服务启动成功
[2025-06-10 14:55:00][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: db43a9c6-2135-4720-954e-7c04518a6255
[2025-06-10 14:55:00][ThreadPoolExecutor-0_0][INFO]: 任务 db43a9c6-2135-4720-954e-7c04518a6255: 音频数量=1, 语言=zh
[2025-06-10 14:55:00][ThreadPoolExecutor-0_0][INFO]: 开始funasr语音识别推理，音频数量: 1, 语言: zh
[2025-06-10 14:55:00][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:55:01][ThreadPoolExecutor-0_0][ERROR]: 100%|[34m##########[0m| 1/1 [00:00<00:00,  2.88it/s]
[2025-06-10 14:55:01][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.035', 'extract_feat': '0.268', 'forward': '0.347', 'batch_size': '1', 'rtf': '0.033'}, : 100%|[34m##########[0m| 1/1 [00:00<00:00,  2.88it/s]
[2025-06-10 14:55:01][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.033: 100%|[34m##########[0m| 1/1 [00:00<00:00,  2.88it/s]
[2025-06-10 14:55:01][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.033: 100%|[34m##########[0m| 1/1 [00:00<00:00,  2.84it/s]
[2025-06-10 14:55:01][ThreadPoolExecutor-0_0][ERROR]: 0%|[31m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:55:01][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/5 [00:00<?, ?it/s]
[2025-06-10 14:55:01][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:55:01][ThreadPoolExecutor-0_0][ERROR]: 100%|[34m##########[0m| 5/5 [00:00<00:00, 15.19it/s]
[2025-06-10 14:55:01][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:55:01][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.000', 'extract_feat': '0.172', 'forward': '0.329', 'batch_size': '5', 'rtf': '0.006'}, : 100%|[34m##########[0m| 5/5 [00:00<00:00, 15.19it/s]
[2025-06-10 14:55:01][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:55:01][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.006: 100%|[34m##########[0m| 5/5 [00:00<00:00, 15.19it/s]
[2025-06-10 14:55:01][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:55:01][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.006: 100%|[34m##########[0m| 5/5 [00:00<00:00, 14.74it/s]
[2025-06-10 14:55:01][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:55:01][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:55:01][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.000', 'extract_feat': '0.012', 'forward': '0.064', 'batch_size': '1', 'rtf': '0.004'}, : 100%|[34m##########[0m| 1/1 [00:00<00:00, 15.51it/s]
[2025-06-10 14:55:01][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:55:01][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004: 100%|[34m##########[0m| 1/1 [00:00<00:00, 15.35it/s]
[2025-06-10 14:55:01][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:55:01][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004: 100%|[34m##########[0m| 1/1 [00:00<00:00, 14.54it/s]
[2025-06-10 14:55:01][ThreadPoolExecutor-0_0][ERROR]: 100%|[31m##########[0m| 1/1 [00:00<00:00,  2.33it/s]
[2025-06-10 14:55:01][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.006, time_speech:  70.471, time_escape: 0.419: 100%|[31m##########[0m| 1/1 [00:00<00:00,  2.33it/s]
[2025-06-10 14:55:01][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.006, time_speech:  70.471, time_escape: 0.419: 100%|[31m##########[0m| 1/1 [00:00<00:00,  2.32it/s]
[2025-06-10 14:55:01][ThreadPoolExecutor-0_0][INFO]: 处理文本: 试错的过程很简单而，且特别是今天报名仓雪卡的同学，你们可以。听到后面的有专门的活动课，他会大大降低你的试错成本。其实你也可以过来听课，为什么你自己写嘛？我先今天写5个点，我就试试试验一下，反正这5个点不行，我再写5个点，这试再不行，那再写5个点嘛。你总会所谓的活动搭神和所谓的高手，都是只有一个把所有的错，所有的坑全部趟一遍，留下正确的你就是所谓的搭神。明白吗？所以说关于活动通过这块，我只送给你们四个字啊，换位思考。如果说你要想降低你的试错成本，今天来这里你们就是对的。因为有畅畅血卡这个机会，所以说关于活动过于不过这个问题，或者活动很难通过这个话题呃，如果真的要坐下来聊的话，要聊一天。😡但是我觉得我刚才说的四个字足够。好，谢谢。好，非常感谢那个三茂老师的回答啊。三茂老师说，我们在整个店铺的这个活动当中，我们要学会换位思考。其实。😊
[2025-06-10 14:55:01][ThreadPoolExecutor-0_0][INFO]: funasr推理原始结果: <|zh|><|ANGRY|><|Speech|><|withitn|>试错的过程很简单而，且特别是今天报名仓雪卡的同学，你们可以。 <|zh|><|ANGRY|><|Speech|><|withitn|>听到后面的有专门的活动课，他会大大降低你的试错成本。其实你也可以过来听课，为什么你自己写嘛？我先今天写5个点，我就试试试验一下，反正这5个点不行，我再写5个点，这试再不行，那再写5个点嘛。 <|zh|><|ANGRY|><|Speech|><|withitn|>你总会所谓的活动搭神和所谓的高手，都是只有一个把所有的错，所有的坑全部趟一遍，留下正确的你就是所谓的搭神。 <|zh|><|ANGRY|><|Speech|><|withitn|>明白吗？所以说关于活动通过这块，我只送给你们四个字啊，换位思考。如果说你要想降低你的试错成本，今天来这里你们就是对的。 <|zh|><|ANGRY|><|Speech|><|withitn|>因为有畅畅血卡这个机会，所以说关于活动过于不过这个问题，或者活动很难通过这个话题呃，如果真的要坐下来聊的话，要聊一天。 <|zh|><|HAPPY|><|Speech|><|withitn|>但是我觉得我刚才说的四个字足够。好，谢谢。好，非常感谢那个三茂老师的回答啊。三茂老师说，我们在整个店铺的这个活动当中，我们要学会换位思考。其实。
[2025-06-10 14:55:01][ThreadPoolExecutor-0_0][INFO]: funasr处理后结果: 试错的过程很简单而，且特别是今天报名仓雪卡的同学，你们可以。听到后面的有专门的活动课，他会大大降低你的试错成本。其实你也可以过来听课，为什么你自己写嘛？我先今天写5个点，我就试试试验一下，反正这5个点不行，我再写5个点，这试再不行，那再写5个点嘛。你总会所谓的活动搭神和所谓的高手，都是只有一个把所有的错，所有的坑全部趟一遍，留下正确的你就是所谓的搭神。明白吗？所以说关于活动通过这块，我只送给你们四个字啊，换位思考。如果说你要想降低你的试错成本，今天来这里你们就是对的。因为有畅畅血卡这个机会，所以说关于活动过于不过这个问题，或者活动很难通过这个话题呃，如果真的要坐下来聊的话，要聊一天。😡但是我觉得我刚才说的四个字足够。好，谢谢。好，非常感谢那个三茂老师的回答啊。三茂老师说，我们在整个店铺的这个活动当中，我们要学会换位思考。其实。😊
[2025-06-10 14:55:01][ThreadPoolExecutor-0_0][INFO]: funasr语音识别完成 - 总耗时: 0.792秒 | 验证: 0.000秒 | 文件准备: 0.003秒 | 模型推理: 0.788秒 | 清理: 0.000秒
[2025-06-10 14:55:01][ThreadPoolExecutor-0_0][INFO]: 任务 db43a9c6-2135-4720-954e-7c04518a6255 完成，处理时间: 0.79秒，结果数量: 1 

[2025-06-10 14:56:10][ThreadPoolExecutor-0_0][INFO]: 收到语音识别请求，任务ID: 777f6e3d-e590-4bd9-93ed-bb52cb4d0ddd
[2025-06-10 14:56:10][ThreadPoolExecutor-0_0][INFO]: 任务 777f6e3d-e590-4bd9-93ed-bb52cb4d0ddd: 音频数量=1, 语言=zh
[2025-06-10 14:56:10][ThreadPoolExecutor-0_0][INFO]: 开始funasr语音识别推理，音频数量: 1, 语言: zh
[2025-06-10 14:56:10][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:56:11][ThreadPoolExecutor-0_0][ERROR]: 100%|[34m##########[0m| 1/1 [00:00<00:00,  3.30it/s]
[2025-06-10 14:56:11][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.010', 'extract_feat': '0.261', 'forward': '0.303', 'batch_size': '1', 'rtf': '0.029'}, : 100%|[34m##########[0m| 1/1 [00:00<00:00,  3.30it/s]
[2025-06-10 14:56:11][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.029: 100%|[34m##########[0m| 1/1 [00:00<00:00,  3.30it/s]
[2025-06-10 14:56:11][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.029: 100%|[34m##########[0m| 1/1 [00:00<00:00,  3.26it/s]
[2025-06-10 14:56:11][ThreadPoolExecutor-0_0][ERROR]: 0%|[31m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:56:11][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/5 [00:00<?, ?it/s]
[2025-06-10 14:56:11][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:56:11][ThreadPoolExecutor-0_0][ERROR]: 100%|[34m##########[0m| 5/5 [00:00<00:00, 21.16it/s]
[2025-06-10 14:56:11][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:56:11][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.000', 'extract_feat': '0.085', 'forward': '0.236', 'batch_size': '5', 'rtf': '0.004'}, : 100%|[34m##########[0m| 5/5 [00:00<00:00, 21.16it/s]
[2025-06-10 14:56:11][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:56:11][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004: 100%|[34m##########[0m| 5/5 [00:00<00:00, 21.16it/s]
[2025-06-10 14:56:11][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:56:11][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004: 100%|[34m##########[0m| 5/5 [00:00<00:00, 20.34it/s]
[2025-06-10 14:56:11][ThreadPoolExecutor-0_0][ERROR]: 0%|[34m          [0m| 0/1 [00:00<?, ?it/s]
[2025-06-10 14:56:11][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:56:11][ThreadPoolExecutor-0_0][ERROR]: {'load_data': '0.000', 'extract_feat': '0.013', 'forward': '0.077', 'batch_size': '1', 'rtf': '0.004'}, : 100%|[34m##########[0m| 1/1 [00:00<00:00, 13.02it/s]
[2025-06-10 14:56:11][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:56:11][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004: 100%|[34m##########[0m| 1/1 [00:00<00:00, 12.89it/s]
[2025-06-10 14:56:11][ThreadPoolExecutor-0_0][ERROR]: [A
[2025-06-10 14:56:11][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.004: 100%|[34m##########[0m| 1/1 [00:00<00:00, 12.34it/s]
[2025-06-10 14:56:11][ThreadPoolExecutor-0_0][ERROR]: 100%|[31m##########[0m| 1/1 [00:00<00:00,  2.89it/s]
[2025-06-10 14:56:11][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.005, time_speech:  70.471, time_escape: 0.336: 100%|[31m##########[0m| 1/1 [00:00<00:00,  2.89it/s]
[2025-06-10 14:56:11][ThreadPoolExecutor-0_0][ERROR]: rtf_avg: 0.005, time_speech:  70.471, time_escape: 0.336: 100%|[31m##########[0m| 1/1 [00:00<00:00,  2.89it/s]
[2025-06-10 14:56:11][ThreadPoolExecutor-0_0][INFO]: 处理文本: 试错的过程很简单而，且特别是今天报名仓雪卡的同学，你们可以。听到后面的有专门的活动课，他会大大降低你的试错成本。其实你也可以过来听课，为什么你自己写嘛？我先今天写5个点，我就试试试验一下，反正这5个点不行，我再写5个点，这试再不行，那再写5个点嘛。你总会所谓的活动搭神和所谓的高手，都是只有一个把所有的错，所有的坑全部趟一遍，留下正确的你就是所谓的搭神。明白吗？所以说关于活动通过这块，我只送给你们四个字啊，换位思考。如果说你要想降低你的试错成本，今天来这里你们就是对的。因为有畅畅血卡这个机会，所以说关于活动过于不过这个问题，或者活动很难通过这个话题呃，如果真的要坐下来聊的话，要聊一天。😡但是我觉得我刚才说的四个字足够。好，谢谢。好，非常感谢那个三茂老师的回答啊。三茂老师说，我们在整个店铺的这个活动当中，我们要学会换位思考。其实。😊
[2025-06-10 14:56:11][ThreadPoolExecutor-0_0][INFO]: funasr推理原始结果: <|zh|><|ANGRY|><|Speech|><|withitn|>试错的过程很简单而，且特别是今天报名仓雪卡的同学，你们可以。 <|zh|><|ANGRY|><|Speech|><|withitn|>听到后面的有专门的活动课，他会大大降低你的试错成本。其实你也可以过来听课，为什么你自己写嘛？我先今天写5个点，我就试试试验一下，反正这5个点不行，我再写5个点，这试再不行，那再写5个点嘛。 <|zh|><|ANGRY|><|Speech|><|withitn|>你总会所谓的活动搭神和所谓的高手，都是只有一个把所有的错，所有的坑全部趟一遍，留下正确的你就是所谓的搭神。 <|zh|><|ANGRY|><|Speech|><|withitn|>明白吗？所以说关于活动通过这块，我只送给你们四个字啊，换位思考。如果说你要想降低你的试错成本，今天来这里你们就是对的。 <|zh|><|ANGRY|><|Speech|><|withitn|>因为有畅畅血卡这个机会，所以说关于活动过于不过这个问题，或者活动很难通过这个话题呃，如果真的要坐下来聊的话，要聊一天。 <|zh|><|HAPPY|><|Speech|><|withitn|>但是我觉得我刚才说的四个字足够。好，谢谢。好，非常感谢那个三茂老师的回答啊。三茂老师说，我们在整个店铺的这个活动当中，我们要学会换位思考。其实。
[2025-06-10 14:56:11][ThreadPoolExecutor-0_0][INFO]: funasr处理后结果: 试错的过程很简单而，且特别是今天报名仓雪卡的同学，你们可以。听到后面的有专门的活动课，他会大大降低你的试错成本。其实你也可以过来听课，为什么你自己写嘛？我先今天写5个点，我就试试试验一下，反正这5个点不行，我再写5个点，这试再不行，那再写5个点嘛。你总会所谓的活动搭神和所谓的高手，都是只有一个把所有的错，所有的坑全部趟一遍，留下正确的你就是所谓的搭神。明白吗？所以说关于活动通过这块，我只送给你们四个字啊，换位思考。如果说你要想降低你的试错成本，今天来这里你们就是对的。因为有畅畅血卡这个机会，所以说关于活动过于不过这个问题，或者活动很难通过这个话题呃，如果真的要坐下来聊的话，要聊一天。😡但是我觉得我刚才说的四个字足够。好，谢谢。好，非常感谢那个三茂老师的回答啊。三茂老师说，我们在整个店铺的这个活动当中，我们要学会换位思考。其实。😊
[2025-06-10 14:56:11][ThreadPoolExecutor-0_0][INFO]: funasr语音识别完成 - 总耗时: 0.659秒 | 验证: 0.000秒 | 文件准备: 0.002秒 | 模型推理: 0.656秒 | 清理: 0.001秒
[2025-06-10 14:56:11][ThreadPoolExecutor-0_0][INFO]: 任务 777f6e3d-e590-4bd9-93ed-bb52cb4d0ddd 完成，处理时间: 0.66秒，结果数量: 1 

[2025-06-10 15:57:01][MainThread][INFO]: 收到停止信号，正在关闭服务...
[2025-06-10 15:57:01][MainThread][INFO]: 服务已停止
