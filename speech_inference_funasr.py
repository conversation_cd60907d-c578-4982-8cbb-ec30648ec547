import os
import re
import time
import logging
import tempfile
from typing import List, Dict, Any, Tuple
from io import BytesIO
import torchaudio
import torch
import numpy as np
from pydub import AudioSegment
from funasr import AutoModel
from funasr.utils.postprocess_utils import rich_transcription_postprocess


class SpeechInferenceFunasr:
    """语音识别推理类 - 使用funasr AutoModel (支持更好的断句)"""

    def __init__(self, model_dir: str = "./SenseVoice_model", device: str = "cuda:0"):
        """
        初始化语音识别推理器

        Args:
            model_dir: 模型目录路径
            device: 推理设备
        """
        self.model_dir = model_dir
        self.device = device
        self.model = None
        self.regex = r"<\|.*\|>"
        self.logger = logging.getLogger("speech_recognition")

        # 支持的语言
        self.supported_languages = ["auto", "zh", "en", "yue", "ja", "ko", "nospeech"]

        self._load_model()

    def _load_model(self):
        """加载语音识别模型"""
        try:
            self.logger.info(f"开始加载funasr语音识别模型，模型路径: {self.model_dir}")
            self.logger.info(f"使用设备: {self.device}")

            start_time = time.time()
            self.model = AutoModel(
                model=self.model_dir,
                trust_remote_code=True,
                remote_code="./model.py",
                vad_model="fsmn-vad",
                vad_kwargs={"max_single_segment_time": 30000},
                device=self.device,
            )

            load_time = time.time() - start_time
            self.logger.info(f"funasr模型加载成功，耗时: {load_time:.2f}秒")

        except Exception as e:
            self.logger.error(f"funasr模型加载失败: {e}")
            raise e

    def _validate_language(self, language: str) -> str:
        """验证并标准化语言参数"""
        if not language or language == "":
            return "auto"

        if language not in self.supported_languages:
            self.logger.warning(f"不支持的语言: {language}，使用默认语言: auto")
            return "auto"

        return language

    def _detect_audio_format(self, audio_bytes: bytes) -> str:
        """
        检测音频格式

        Args:
            audio_bytes: 音频文件的字节数据

        Returns:
            音频格式 ('wav', 'mp3', 'flac', 等)
        """
        # 检查文件头来判断格式
        if audio_bytes.startswith(b'RIFF') and b'WAVE' in audio_bytes[:12]:
            return 'wav'
        elif audio_bytes.startswith(b'ID3') or audio_bytes.startswith(b'\xff\xfb') or audio_bytes.startswith(b'\xff\xf3') or audio_bytes.startswith(b'\xff\xf2'):
            return 'mp3'
        elif audio_bytes.startswith(b'fLaC'):
            return 'flac'
        elif audio_bytes.startswith(b'OggS'):
            return 'ogg'
        else:
            # 默认尝试wav格式
            return 'wav'

    def _save_temp_audio_file(self, audio_bytes: bytes, audio_format: str) -> str:
        """
        将音频字节数据保存为临时文件
        funasr的AutoModel需要文件路径作为输入

        Args:
            audio_bytes: 音频文件的字节数据
            audio_format: 音频格式

        Returns:
            临时文件路径
        """
        try:
            # 创建临时文件
            suffix = f".{audio_format}"
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=suffix)

            # 写入音频数据
            temp_file.write(audio_bytes)
            temp_file.close()

            return temp_file.name

        except Exception as e:
            self.logger.error(f"创建临时音频文件失败: {e}")
            raise e

    def _calculate_audio_duration_from_file(self, file_path: str) -> float:
        """从文件路径计算音频时长"""
        try:
            # 使用torchaudio获取音频信息
            info = torchaudio.info(file_path)
            duration = info.num_frames / info.sample_rate
            return duration
        except Exception:
            # 如果torchaudio失败，使用pydub
            try:
                audio = AudioSegment.from_file(file_path)
                return len(audio) / 1000.0  # 转换为秒
            except Exception as e:
                self.logger.warning(f"无法计算音频时长: {e}")
                return 0.0

    def inference(self,
                  audio_files: List[bytes],
                  audio_keys: List[str] = None,
                  language: str = "auto",
                  use_itn: bool = True,
                  ban_emo_unk: bool = False,
                  batch_size_s: int = 60,
                  merge_vad: bool = True,
                  merge_length_s: int = 15) -> List[Dict[str, Any]]:
        """
        执行语音识别推理 - 使用funasr AutoModel

        Args:
            audio_files: 音频文件字节数据列表
            audio_keys: 音频文件名称列表
            language: 语言类型
            use_itn: 是否使用逆文本标准化
            ban_emo_unk: 是否禁用情感和未知标记 (funasr中不使用)
            batch_size_s: 批处理大小(秒)
            merge_vad: 是否合并VAD结果
            merge_length_s: 合并长度(秒)

        Returns:
            识别结果列表
        """
        try:
            total_start_time = time.time()

            # 1. 输入验证阶段
            validation_start = time.time()
            if not audio_files:
                self.logger.warning("音频文件列表为空")
                return []

            # 处理音频键名
            if not audio_keys or len(audio_keys) != len(audio_files):
                audio_keys = [f"audio_{i}" for i in range(len(audio_files))]

            # 验证语言参数
            language = self._validate_language(language)
            validation_time = time.time() - validation_start

            # 2. 音频文件准备阶段
            file_preparation_start = time.time()
            temp_files = []
            audio_durations = []

            try:
                for i, audio_bytes in enumerate(audio_files):
                    try:
                        # 检测音频格式
                        audio_format = self._detect_audio_format(audio_bytes)

                        # 创建临时文件
                        temp_file_path = self._save_temp_audio_file(audio_bytes, audio_format)
                        temp_files.append(temp_file_path)

                        # 计算音频时长
                        duration = self._calculate_audio_duration_from_file(temp_file_path)
                        audio_durations.append(duration)

                        self.logger.debug(f"音频 {audio_keys[i]} 准备完成: {audio_format}格式, {duration:.2f}秒")

                    except Exception as e:
                        self.logger.error(f"准备音频 {audio_keys[i]} 失败: {e}")
                        continue

                if not temp_files:
                    self.logger.error("没有成功准备的音频文件")
                    return []

                file_preparation_time = time.time() - file_preparation_start

                # 3. 模型推理阶段
                self.logger.info(f"开始funasr语音识别推理，音频数量: {len(temp_files)}, 语言: {language}")
                inference_start = time.time()

                results = []
                for i, temp_file_path in enumerate(temp_files):
                    try:
                        # 使用funasr进行推理
                        res = self.model.generate(
                            input=temp_file_path,
                            cache={},
                            language=language,
                            use_itn=use_itn,
                            batch_size_s=batch_size_s,
                            merge_vad=merge_vad,
                            merge_length_s=merge_length_s,
                        )

                        # 处理结果
                        if res and len(res) > 0 and "text" in res[0]:
                            raw_text = res[0]["text"]

                            # 后处理文本 - funasr的优势在于更好的断句
                            processed_text = rich_transcription_postprocess(raw_text)
                            # print(f"处理文本: {processed_text}")

                            # 清理文本中的换行符和多余空格，但保持标点符号
                            processed_text = processed_text.replace('\n', ' ').replace('\r', ' ').strip()
                            processed_text = ' '.join(processed_text.split())  # 合并多个空格为一个

                            # 清理文本（去除特殊标记）- 用于clean_text字段
                            clean_text = re.sub(self.regex, "", raw_text, 0, re.MULTILINE)
                            clean_text = clean_text.replace('\n', ' ').replace('\r', ' ').strip()
                            clean_text = ' '.join(clean_text.split())

                            self.logger.info(f'funasr推理原始结果: {raw_text}')
                            self.logger.info(f'funasr处理后结果: {processed_text}')

                            # 构建结果
                            result = {
                                "key": audio_keys[i] if i < len(audio_keys) else f"audio_{i}",
                                "raw_text": raw_text,
                                "clean_text": clean_text,
                                "text": processed_text,  # 这里使用rich_transcription_postprocess的结果
                                "confidence": res[0].get("confidence", 0.0) if "confidence" in res[0] else 0.0,
                                "duration": audio_durations[i] if i < len(audio_durations) else 0.0,
                                "language": language
                            }
                            results.append(result)

                        else:
                            self.logger.warning(f"音频 {audio_keys[i]} 推理返回空结果")

                    except Exception as e:
                        self.logger.error(f"推理音频 {audio_keys[i]} 失败: {e}")
                        continue

                inference_time = time.time() - inference_start

                # 4. 清理临时文件
                cleanup_start = time.time()
                for temp_file in temp_files:
                    try:
                        os.unlink(temp_file)
                    except Exception as e:
                        self.logger.warning(f"删除临时文件失败 {temp_file}: {e}")
                cleanup_time = time.time() - cleanup_start

                total_time = time.time() - total_start_time

                # 详细的性能日志
                self.logger.info(f"funasr语音识别完成 - 总耗时: {total_time:.3f}秒 | "
                               f"验证: {validation_time:.3f}秒 | "
                               f"文件准备: {file_preparation_time:.3f}秒 | "
                               f"模型推理: {inference_time:.3f}秒 | "
                               f"清理: {cleanup_time:.3f}秒")

                # 性能警告
                if file_preparation_time > inference_time:
                    self.logger.warning(f"文件准备耗时({file_preparation_time:.3f}秒)高于推理耗时({inference_time:.3f}秒)")

                return results

            finally:
                # 确保清理临时文件
                for temp_file in temp_files:
                    try:
                        if os.path.exists(temp_file):
                            os.unlink(temp_file)
                    except Exception:
                        pass

        except Exception as e:
            self.logger.error(f"funasr语音识别推理失败: {e}")
            raise e

    def health_check(self) -> bool:
        """健康检查"""
        try:
            return self.model is not None and torch.cuda.is_available()
        except Exception:
            return False
