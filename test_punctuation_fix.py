#!/usr/bin/env python3
"""
测试断句修复效果的脚本
对比use_itn=True和use_itn=False的效果
"""

from speech_grpc_client import SpeechRecognitionClient
from pathlib import Path
import re

def count_punctuation(text):
    """统计文本中的标点符号数量"""
    sentences = text.count('。') + text.count('！') + text.count('？')
    commas = text.count('，') + text.count(',')
    return sentences, commas

def test_punctuation_fix():
    """测试断句修复效果"""
    print("=" * 80)
    print("断句修复效果测试")
    print("=" * 80)
    
    # 测试音频文件
    test_file = "./voice_test/vad_example.wav"
    
    if not Path(test_file).exists():
        print(f"❌ 测试文件不存在: {test_file}")
        return
    
    print(f"📁 测试文件: {test_file}")
    
    # 创建客户端
    client = SpeechRecognitionClient("localhost:50051")
    
    try:
        # 健康检查
        print("\n🔍 执行健康检查...")
        health_result = client.health_check()
        if health_result['code'] != 200:
            print(f"❌ 服务不可用: {health_result}")
            return
        print("✅ 服务正常")
        
        # 测试1: use_itn=False (无断句)
        print("\n" + "=" * 50)
        print("测试1: use_itn=False (无断句标点符号)")
        print("=" * 50)
        
        result_false = client.recognize_speech(
            audio_files=[test_file],
            language="zh",
            use_itn=False,  # 不使用逆文本标准化
            ban_emo_unk=False
        )
        
        if result_false['code'] == 200 and result_false['results']:
            res = result_false['results'][0]
            sentences, commas = count_punctuation(res['text'])
            
            print(f"✅ 识别成功")
            print(f"   处理时间: {result_false['processing_time']:.3f}秒")
            print(f"   文本长度: {len(res['text'])} 字符")
            print(f"   句号数量: {sentences}")
            print(f"   逗号数量: {commas}")
            print(f"   文本预览: {res['text'][:100]}...")
            
            # 检查原始文本中的标记
            if '<|woitn|>' in res['raw_text']:
                print("   ✓ 确认使用了 use_itn=False (标记: <|woitn|>)")
            elif '<|withitn|>' in res['raw_text']:
                print("   ⚠️  意外使用了 use_itn=True (标记: <|withitn|>)")
        else:
            print(f"❌ 识别失败: {result_false.get('message', '未知错误')}")
            return
        
        # 测试2: use_itn=True (有断句)
        print("\n" + "=" * 50)
        print("测试2: use_itn=True (保留断句标点符号)")
        print("=" * 50)
        
        result_true = client.recognize_speech(
            audio_files=[test_file],
            language="zh",
            use_itn=True,  # 使用逆文本标准化
            ban_emo_unk=False
        )
        
        if result_true['code'] == 200 and result_true['results']:
            res = result_true['results'][0]
            sentences, commas = count_punctuation(res['text'])
            
            print(f"✅ 识别成功")
            print(f"   处理时间: {result_true['processing_time']:.3f}秒")
            print(f"   文本长度: {len(res['text'])} 字符")
            print(f"   句号数量: {sentences}")
            print(f"   逗号数量: {commas}")
            print(f"   文本预览: {res['text'][:100]}...")
            
            # 检查原始文本中的标记
            if '<|withitn|>' in res['raw_text']:
                print("   ✓ 确认使用了 use_itn=True (标记: <|withitn|>)")
            elif '<|woitn|>' in res['raw_text']:
                print("   ⚠️  意外使用了 use_itn=False (标记: <|woitn|>)")
        else:
            print(f"❌ 识别失败: {result_true.get('message', '未知错误')}")
            return
        
        # 对比结果
        print("\n" + "=" * 50)
        print("对比结果")
        print("=" * 50)
        
        false_res = result_false['results'][0]
        true_res = result_true['results'][0]
        
        false_sentences, false_commas = count_punctuation(false_res['text'])
        true_sentences, true_commas = count_punctuation(true_res['text'])
        
        print(f"📊 标点符号统计:")
        print(f"   use_itn=False: 句号={false_sentences}, 逗号={false_commas}")
        print(f"   use_itn=True:  句号={true_sentences}, 逗号={true_commas}")
        print(f"   改善效果: 句号+{true_sentences-false_sentences}, 逗号+{true_commas-false_commas}")
        
        print(f"\n⏱️  性能对比:")
        print(f"   use_itn=False: {result_false['processing_time']:.3f}秒")
        print(f"   use_itn=True:  {result_true['processing_time']:.3f}秒")
        print(f"   时间差异: {abs(result_true['processing_time'] - result_false['processing_time']):.3f}秒")
        
        # 结论
        print(f"\n🎯 测试结论:")
        if true_sentences > false_sentences or true_commas > false_commas:
            print("   ✅ 断句修复成功！use_itn=True 显著改善了断句效果")
        else:
            print("   ❌ 断句修复可能存在问题")
        
        if abs(result_true['processing_time'] - result_false['processing_time']) < 0.1:
            print("   ✅ 性能影响很小，可以接受")
        else:
            print("   ⚠️  性能有一定影响")
            
    except Exception as e:
        print(f"❌ 测试过程中发生异常: {e}")
    
    finally:
        client.close()
        print(f"\n🔚 测试完成")

if __name__ == "__main__":
    test_punctuation_fix()
